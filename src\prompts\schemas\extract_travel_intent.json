{"type": "object", "properties": {"destination": {"type": ["string", "null"], "description": "目的地城市名称，如果用户没有明确提及则为null"}, "origin": {"type": ["string", "null"], "description": "出发地城市名称，如果用户没有明确提及则为null"}, "days": {"type": ["integer", "null"], "description": "出行天数，必须为整数类型，如果没有明确提及则为null", "minimum": 1, "maximum": 30}, "travel_time": {"type": ["string", "null"], "description": "出行时间，可以是具体日期或相对时间表述"}, "travelers": {"type": ["string", "null"], "description": "出行人员描述，包括人数和关系类型"}, "budget": {"type": ["string", "null"], "enum": ["经济型", "中等", "豪华型", null], "description": "预算偏好类型"}, "preferences": {"type": "array", "items": {"type": "string"}, "description": "兴趣偏好列表，如文化历史、自然风光、美食体验等"}, "special_needs": {"type": "array", "items": {"type": "string"}, "description": "特殊需求列表，如无障碍、亲子友好、宠物友好等"}, "transport_mode": {"type": "string", "enum": ["driving"], "description": "出行方式，固定为driving（开车）"}}, "required": ["transport_mode"], "additionalProperties": false}