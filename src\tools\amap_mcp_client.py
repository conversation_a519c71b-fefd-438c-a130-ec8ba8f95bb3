"""
高德地图MCP客户端

使用正确的MCP协议调用高德地图服务，提供地理定位、路线规划、POI搜索等功能。
"""
import asyncio
import json
from typing import Dict, Any, Optional, List
from contextlib import AsyncExitStack, asynccontextmanager
from mcp import ClientSession
from mcp.client.sse import sse_client
from src.core.logger import get_logger

logger = get_logger("amap_mcp_client")


class AmapMCPClient:
    """高德地图MCP客户端 - 使用正确的MCP协议"""
    
    def __init__(self, api_key: Optional[str] = None):
        """
        初始化高德MCP客户端
        
        Args:
            api_key: 高德API密钥，如果不提供则使用默认密钥
        """
        self.api_key = api_key or "cd978c562fe54dd9a11117bfd4a2a3f1"
        self.mcp_url = f"https://mcp.amap.com/sse?key={self.api_key}"
        self._exit_stack: Optional[AsyncExitStack] = None
        self.session: Optional[ClientSession] = None
        self.tools: Dict[str, Any] = {}
        self.is_connected = False
        self.logger = logger
        self._connection_lock = asyncio.Lock()
        self._max_retries = 3
        self._retry_delay = 1.0
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.disconnect()
        
    async def connect(self):
        """建立MCP连接（带重试机制）"""
        async with self._connection_lock:
            if self.is_connected:
                return
                
            for attempt in range(self._max_retries):
                try:
                    self.logger.info(f"正在连接到高德MCP服务: {self.mcp_url} (尝试 {attempt + 1}/{self._max_retries})")
                    
                    self._exit_stack = AsyncExitStack()
                    
                    # 1. 建立SSE连接，增加超时设置
                    sse_cm = sse_client(self.mcp_url)
                    streams = await asyncio.wait_for(
                        self._exit_stack.enter_async_context(sse_cm),
                        timeout=30.0  # 30秒连接超时
                    )
                    
                    # 2. 创建MCP客户端会话
                    session_cm = ClientSession(streams[0], streams[1])
                    self.session = await self._exit_stack.enter_async_context(session_cm)
                    
                    # 3. 初始化会话，设置超时
                    await asyncio.wait_for(
                        self.session.initialize(),
                        timeout=15.0  # 15秒初始化超时
                    )
                    
                    # 4. 获取可用工具列表
                    response = await asyncio.wait_for(
                        self.session.list_tools(),
                        timeout=10.0  # 10秒工具列表超时
                    )
                    self.tools = {tool.name: tool for tool in response.tools}
                    
                    self.is_connected = True
                    self.logger.info(f"高德MCP客户端连接成功，获取到 {len(self.tools)} 个工具")
                    return
                    
                except asyncio.TimeoutError:
                    self.logger.warning(f"MCP连接超时 (尝试 {attempt + 1}/{self._max_retries})")
                    await self._cleanup_connection()
                    if attempt < self._max_retries - 1:
                        await asyncio.sleep(self._retry_delay * (attempt + 1))
                    continue
                    
                except Exception as e:
                    self.logger.warning(f"MCP连接失败 (尝试 {attempt + 1}/{self._max_retries}): {e}")
                    await self._cleanup_connection()
                    if attempt < self._max_retries - 1:
                        await asyncio.sleep(self._retry_delay * (attempt + 1))
                    continue
                    
            # 所有重试都失败了
            self.logger.error("高德MCP连接失败，已达到最大重试次数")
            raise ConnectionError("无法连接到高德MCP服务")
            
    async def _cleanup_connection(self):
        """清理连接资源"""
        try:
            if self._exit_stack:
                await self._exit_stack.aclose()
                self._exit_stack = None
            self.session = None
            self.is_connected = False
        except Exception as e:
            self.logger.debug(f"清理连接时出错: {e}")
            
    async def disconnect(self):
        """断开MCP连接"""
        async with self._connection_lock:
            await self._cleanup_connection()
            self.logger.info("高德MCP客户端已断开")
            
    async def _call_mcp_tool(self, tool_name: str, parameters: Dict[str, Any]) -> str:
        """
        调用MCP工具（带重连机制）
        
        Args:
            tool_name: 工具名称
            parameters: 工具参数
            
        Returns:
            工具响应的文本内容
        """
        for attempt in range(self._max_retries):
            try:
                # 确保连接可用
                if not self.is_connected:
                    await self.connect()
                    
                if tool_name not in self.tools:
                    available_tools = list(self.tools.keys())
                    raise ValueError(f"工具 {tool_name} 不存在。可用工具: {available_tools}")
                
                self.logger.debug(f"调用MCP工具: {tool_name} (尝试 {attempt + 1})", extra={
                    "tool_name": tool_name,
                    "parameters": parameters
                })
                
                # 设置工具调用超时
                result = await asyncio.wait_for(
                    self.session.call_tool(tool_name, parameters),
                    timeout=20.0  # 20秒工具调用超时
                )
                
                # 提取文本内容
                response_text = result.content[0].text if result.content else ""
                
                self.logger.debug(f"MCP工具调用成功: {tool_name}", extra={
                    "tool_name": tool_name,
                    "response_size": len(response_text)
                })
                
                return response_text
                
            except asyncio.TimeoutError:
                self.logger.warning(f"MCP工具调用超时: {tool_name} (尝试 {attempt + 1}/{self._max_retries})")
                self.is_connected = False  # 标记连接为无效
                if attempt < self._max_retries - 1:
                    await asyncio.sleep(self._retry_delay)
                    continue
                else:
                    raise ConnectionError(f"工具调用超时: {tool_name}")
                    
            except Exception as e:
                self.logger.warning(f"MCP工具调用失败: {tool_name} (尝试 {attempt + 1}/{self._max_retries}): {e}")
                self.is_connected = False  # 标记连接为无效
                if attempt < self._max_retries - 1:
                    await asyncio.sleep(self._retry_delay)
                    continue
                else:
                    self.logger.error(f"MCP工具调用最终失败: {tool_name}", extra={
                        "tool_name": tool_name,
                        "parameters": parameters,
                        "error": str(e)
                    })
                    raise
            
    # 地理编码和逆地理编码
    async def maps_geo(self, address: str, city: Optional[str] = None) -> Dict[str, Any]:
        """地址转坐标"""
        params = {"address": address}
        if city:
            params["city"] = city
        
        response_text = await self._call_mcp_tool("maps_geo", params)
        try:
            return json.loads(response_text)
        except json.JSONDecodeError:
            self.logger.warning(f"maps_geo响应解析失败: {response_text}")
            return {"error": response_text}
        
    async def maps_regeocode(self, longitude: float, latitude: float) -> Dict[str, Any]:
        """坐标转地址"""
        params = {
            "location": f"{longitude},{latitude}"
        }
        response_text = await self._call_mcp_tool("maps_regeocode", params)
        try:
            return json.loads(response_text)
        except json.JSONDecodeError:
            return {"error": response_text}
        
    async def maps_ip_location(self, ip: Optional[str] = None) -> Dict[str, Any]:
        """IP定位"""
        params = {}
        if ip:
            params["ip"] = ip
        response_text = await self._call_mcp_tool("maps_ip_location", params)
        try:
            return json.loads(response_text)
        except json.JSONDecodeError:
            return {"error": response_text}
        
    # 路线规划
    async def maps_direction_driving(
        self, 
        origin: str, 
        destination: str, 
        waypoints: Optional[str] = None,
        strategy: int = 0
    ) -> Dict[str, Any]:
        """驾车路线规划 - 需要经纬度坐标格式：经度,纬度"""
        params = {
            "origin": origin,
            "destination": destination
        }
        if waypoints:
            params["waypoints"] = waypoints
            
        response_text = await self._call_mcp_tool("maps_direction_driving", params)
        try:
            return json.loads(response_text)
        except json.JSONDecodeError:
            return {"error": response_text}
        
    async def maps_direction_walking(
        self, 
        origin: str, 
        destination: str
    ) -> Dict[str, Any]:
        """步行路线规划"""
        params = {
            "origin": origin,
            "destination": destination
        }
        response_text = await self._call_mcp_tool("maps_direction_walking", params)
        try:
            return json.loads(response_text)
        except json.JSONDecodeError:
            return {"error": response_text}
        
    async def maps_direction_bicycling(
        self, 
        origin: str, 
        destination: str
    ) -> Dict[str, Any]:
        """骑行路线规划"""
        params = {
            "origin": origin,
            "destination": destination
        }
        response_text = await self._call_mcp_tool("maps_direction_bicycling", params)
        try:
            return json.loads(response_text)
        except json.JSONDecodeError:
            return {"error": response_text}
        
    async def maps_direction_transit_integrated(
        self, 
        origin: str, 
        destination: str,
        city: str,
        cityd: Optional[str] = None
    ) -> Dict[str, Any]:
        """公共交通路线规划"""
        params = {
            "origin": origin,
            "destination": destination,
            "city": city
        }
        if cityd:
            params["cityd"] = cityd
        response_text = await self._call_mcp_tool("maps_direction_transit_integrated", params)
        try:
            return json.loads(response_text)
        except json.JSONDecodeError:
            return {"error": response_text}
        
    # 搜索服务
    async def maps_text_search(
        self, 
        keywords: str, 
        city: Optional[str] = None,
        page: int = 1,
        offset: int = 20
    ) -> Dict[str, Any]:
        """关键字搜索"""
        params = {
            "keywords": keywords,
            "page": page,
            "offset": offset
        }
        if city:
            params["city"] = city
        response_text = await self._call_mcp_tool("maps_text_search", params)
        try:
            return json.loads(response_text)
        except json.JSONDecodeError:
            return {"error": response_text}
        
    async def maps_around_search(
        self, 
        location: str, 
        keywords: str,
        radius: int = 1000,
        page: int = 1,
        offset: int = 20
    ) -> Dict[str, Any]:
        """周边搜索"""
        params = {
            "location": location,
            "keywords": keywords,
            "radius": radius,
            "page": page,
            "offset": offset
        }
        response_text = await self._call_mcp_tool("maps_around_search", params)
        try:
            return json.loads(response_text)
        except json.JSONDecodeError:
            return {"error": response_text}
        
    async def maps_search_detail(self, id: str) -> Dict[str, Any]:
        """POI详情查询"""
        params = {"id": id}
        response_text = await self._call_mcp_tool("maps_search_detail", params)
        try:
            return json.loads(response_text)
        except json.JSONDecodeError:
            return {"error": response_text}
        
    # 辅助服务
    async def maps_weather(self, city: str) -> Dict[str, Any]:
        """天气查询"""
        params = {"city": city}
        response_text = await self._call_mcp_tool("maps_weather", params)
        try:
            data = json.loads(response_text)
            # 正确的检查：只验证核心数据'forecasts'是否存在且不为空
            if "forecasts" not in data or not data["forecasts"]:
                self.logger.warning(f"maps_weather查询无有效数据: city={city}, response={response_text}")
                return {"status": "0", "forecasts": [], "info": data.get("info", "NO_DATA")}
            # 如果有数据，补充一个成功的状态码，方便上游统一处理
            data.setdefault("status", "1")
            return data
        except json.JSONDecodeError:
            self.logger.error(f"maps_weather响应JSON解析失败: {response_text}")
            return {"status": "0", "forecasts": [], "info": "JSON_DECODE_ERROR"}
        
    async def maps_distance(
        self, 
        origins: str, 
        destination: str,
        type: int = 1
    ) -> Dict[str, Any]:
        """距离测量"""
        params = {
            "origins": origins,
            "destination": destination,
            "type": type
        }
        response_text = await self._call_mcp_tool("maps_distance", params)
        try:
            return json.loads(response_text)
        except json.JSONDecodeError:
            return {"error": response_text}
        
    # App唤醒服务
    async def maps_schema_personal_map(
        self, 
        pois: str,
        name: Optional[str] = None
    ) -> Dict[str, Any]:
        """生成个人地图链接"""
        params = {"pois": pois}
        if name:
            params["name"] = name
        response_text = await self._call_mcp_tool("maps_schema_personal_map", params)
        try:
            return json.loads(response_text)
        except json.JSONDecodeError:
            return {"error": response_text}
        
    async def maps_schema_navi(
        self, 
        poiname: str,
        poiid: Optional[str] = None,
        lat: Optional[float] = None,
        lon: Optional[float] = None
    ) -> Dict[str, Any]:
        """生成导航链接"""
        params = {"poiname": poiname}
        if poiid:
            params["poiid"] = poiid
        if lat and lon:
            params["lat"] = lat
            params["lon"] = lon
        response_text = await self._call_mcp_tool("maps_schema_navi", params)
        try:
            return json.loads(response_text)
        except json.JSONDecodeError:
            return {"error": response_text}
        
    async def maps_schema_take_taxi(
        self, 
        dlat: float, 
        dlon: float,
        dname: str,
        slat: Optional[float] = None,
        slon: Optional[float] = None,
        sname: Optional[str] = None
    ) -> Dict[str, Any]:
        """生成打车链接"""
        params = {
            "dlat": dlat,
            "dlon": dlon,
            "dname": dname
        }
        if slat and slon:
            params["slat"] = slat
            params["slon"] = slon
        if sname:
            params["sname"] = sname
        response_text = await self._call_mcp_tool("maps_schema_take_taxi", params)
        try:
            return json.loads(response_text)
        except json.JSONDecodeError:
            return {"error": response_text}


# 全局客户端实例
_global_client: Optional[AmapMCPClient] = None


async def get_amap_client() -> AmapMCPClient:
    """获取全局高德MCP客户端实例"""
    global _global_client
    if _global_client is None:
        _global_client = AmapMCPClient()
        await _global_client.connect()
    return _global_client


@asynccontextmanager
async def amap_client():
    """高德MCP客户端上下文管理器"""
    client = AmapMCPClient()
    try:
        await client.connect()
        yield client
    finally:
        await client.disconnect()
