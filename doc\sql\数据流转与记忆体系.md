# AutoPilot AI - 数据流转与记忆体系

本文档详细描述了 AutoPilot AI 系统中，一次完整的用户交互（从请求发起到任务结束）所涉及的核心数据流转、状态管理以及与分层记忆系统的交互机制。该设计旨在确保高并发下的实时性能、数据一致性与长期记忆的有效沉淀。

## 核心原则

- **状态与持久化分离**：使用 Redis 作为高性能的"实时作战室"，处理任务执行过程中的高频读写；使用 MySQL 和 MongoDB 作为"永久档案馆"，负责任务的最终归档和长期记忆的持久化。
- **任务ID贯穿始终**：一个全局唯一的 `task_id` 是串联所有系统（Redis, MySQL, MongoDB）和日志的关键，确保了端到端的可追溯性。
- **异步归档**：核心交互流程不应被数据库的写入延迟阻塞。数据的持久化（归档）作为任务结束后的异步步骤，与用户响应解耦。
- **明确责任主体**：引入**任务协调器 (Task Coordinator)** 的概念（通常由API网关或一个独立的生命周期管理服务承担），它全权负责任务的创建、状态变更、归档和清理。AI Agent则专注于执行业务逻辑，通过标准接口汇报进度，实现核心业务与基础设施的关注点分离。

## 端到端数据流转图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Vehicle as 车端
    participant Coordinator as 任务协调器<br/>(API网关/生命周期服务)
    participant Redis as L0/L1: 即时/短期记忆<br/>(作战室/缓存)
    participant Engine as AutoPilot AI引擎<br/>(Agent集群)
    participant MySQL as L2: 结构化记忆<br/>(用户档案)
    participant MongoDB as L2: 非结构化记忆<br/>(交互日志)

    User->>Vehicle: 发起复杂请求 (如 "规划周末亲子游")
    Vehicle->>+Coordinator: POST /v1/tasks (封装请求和车端上下文)
    
    Coordinator->>Coordinator: 1. 生成全局 task_id
    
    par "三方占坑 (Initiation)"
        Coordinator->>+MySQL: INSERT `ai_planning_sessions` (task_id, status: 'PROCESSING')
        Coordinator->>+MongoDB: insertOne `ai_interaction_logs` (骨架文档: task_id, status: 'PROCESSING')
        Coordinator->>+Redis: HSET `task:{task_id}` (设置初始状态, TTL 30min)
    end
    
    MySQL-->>-Coordinator: OK
    MongoDB-->>-Coordinator: OK
    Redis-->>-Coordinator: OK
    Coordinator-->>-Vehicle: HTTP 202 (返回 task_id 和 SSE stream_url)
    
    Vehicle->>Coordinator: GET /v1/stream/{task_id} (建立SSE连接)
    
    Note over Coordinator, Engine: 协调器触发AI引擎执行任务
    Coordinator->>Engine: run_task(task_id)

    loop "AI引擎执行 (Execution)"
        Note over Engine, Redis: L0: 实时状态写入Redis
        Engine->>Redis: HSET `task:{task_id}` (更新Agent执行步骤、中间结果、进度)
        
        alt "外部工具调用"
            Note over Engine, Redis: 优先查询L1缓存
            Engine->>Redis: GET `cache:tool:{tool_name}:{params_hash}`
            alt "缓存命中"
                Redis-->>Engine: 返回缓存结果
            else "缓存未命中"
                Engine->>Engine: 调用外部API
                Engine->>Redis: SET `cache:tool:{...}` (结果写入缓存, 设置TTL)
            end
        end
        
        alt "工具调用失败"
            Engine->>Engine: 自动重试 (如3次)
            Note right of Engine: 若重试成功则继续
        else "重试全部失败或发生严重错误"
            Engine->>Coordinator: report_failure(task_id, error_details)
            break 异常退出
        end

        Note over Redis, Vehicle: SSE推送实时进度
        Redis-->>Coordinator: (Pub/Sub or Polling)
        Coordinator-->>Vehicle: event: status_update (推送Redis中的最新进度)
        
        Note over Engine, MySQL: L2: 检索长期记忆
        Engine->>MySQL: SELECT * FROM `user_memories` WHERE user_id = ?
        Engine->>MySQL: SELECT * FROM `user_summaries` WHERE user_id = ?
        MySQL-->>Engine: 返回用户画像和记忆片段
        
        Engine->>Engine: 融合记忆，执行智能决策 (如景点精选、顺序优化)

        alt "交互式确认环节 (可选)"
            Engine->>Redis: HSET `task:{task_id}` (存入推荐选项, 任务状态变为 'WAITING_FOR_INPUT')
            Coordinator-->>Vehicle: event: interactive_choice_required (payload: {options...})
            Note over Engine: 引擎暂停, 等待用户输入
            
            Vehicle->>User: 展示选项
            Vehicle->>+Coordinator: POST /v1/tasks/{task_id}/continue (payload: {user_choice})
            Coordinator->>Redis: HSET `task:{task_id}` (更新用户选择, 任务状态恢复为 'PROCESSING')
            Coordinator->>Engine: resume_task(task_id)
            Note over Engine: 引擎根据用户选择继续执行
        end
    end

    Note over Engine, Coordinator: 任务完成
    Engine->>Coordinator: report_success(task_id, final_result)

    par "数据归档 (Archiving)"
        Coordinator->>+Redis: HGETALL `task:{task_id}` (获取完整执行记录)
        Redis-->>-Coordinator: 返回所有中间数据
        
        Coordinator->>+MongoDB: updateOne `ai_interaction_logs` (用Redis数据填充完整日志)
        MongoDB-->>-Coordinator: OK

        Coordinator->>+MySQL: UPDATE `ai_planning_sessions` (status: 'SUCCESS')
        MySQL-->>-Coordinator: OK

        Coordinator->>Redis: DEL `task:{task_id}`
    end

    Note over Engine, MySQL: 记忆沉淀 (Learning)
    Engine->>Engine: 调用记忆生成/评估Agent
    Engine->>MySQL: INSERT/UPDATE `user_memories` (新增或更新用户记忆)
    Engine->>MySQL: UPDATE `user_summaries` (更新用户画像摘要)

    Coordinator-->>Vehicle: event: task_result (推送最终结果)
    Coordinator-->>Vehicle: event: close (关闭SSE连接)

    subgraph ExceptionHandling ["异常处理流程"]
        direction LR
        A_FAIL["引擎上报失败"]
        B_CANCEL["用户主动取消 (API)"]
        C_TIMEOUT["任务超时"]

        A_FAIL --> D_NOTIFY
        B_CANCEL --> D_NOTIFY
        C_TIMEOUT --> D_NOTIFY

        D_NOTIFY("1. 协调器通知用户<br/>(SSE event: task_failed)")
        D_NOTIFY --> E_ARCHIVE("2. 协调器归档异常信息<br/>(Redis -> MongoDB/MySQL)")
        E_ARCHIVE --> F_CLEAN("3. 协调器清理Redis资源<br/>(DEL task:{task_id})")
    end

```

## 数据流转详解

### 阶段一：任务初始化 (三方占坑)

当用户发起一个需要 AutoPilot AI 处理的复杂任务时，系统首先确保该任务的"身份"被牢固确立，即使后续处理失败，也能追踪到记录。

1.  **生成唯一ID**: **任务协调器**为本次交互生成一个全局唯一的 `task_id` (例如: `UUID`)。
2.  **MySQL 占坑**: 在 `ai_planning_sessions` 表中插入一条新记录。此记录的核心是 `task_id` 和一个初始状态，如 `'PROCESSING'`。这提供了一个可靠的、事务性的任务总览。
3.  **MongoDB 占坑**: 在 `ai_interaction_logs` 集合中插入一个"骨架"文档。该文档仅包含 `task_id`、`user_id` 和初始状态，为未来的详细日志归档预留了位置。
4.  **Redis 创建"作战室"**: 在 Redis 中创建一个 **Hash**，其 `key` 为 `task:{task_id}`。这个 Hash 将作为本次任务执行期间所有动态数据的"实时白板"。同时，为其设置一个合理的过期时间（如30分钟），作为超时自动清理的保险措施。

完成这三步后，任务的持久化记录和实时处理空间均已备好。系统可以安全地向客户端返回 `task_id`，并开始真正的智能处理。

#### 性能优先的备选方案 (可选)

为了极致的响应速度，可以采用"先响应、后占坑"的策略：

-   **流程**: 协调器接收到请求后，**只先同步写入Redis**（内存操作，极快），然后立刻返回`HTTP 202 Accepted`给客户端。之后，通过一个可靠的**异步任务**（如消息队列或后台线程），去完成对MySQL和MongoDB的"占坑"写入。
-   **权衡**: 这是一个**一致性与性能的权衡**。它极大地降低了用户初始请求的等待时间，但存在一个极小的时间窗口：如果异步写入数据库失败，任务记录可能会丢失。在实践中，配合完善的监控和重试机制，这种风险是可控且可接受的。

### 阶段二：任务执行 (实时读写 Redis)

此阶段是 AI 引擎的核心工作区域，所有操作都围绕高性能的 Redis 进行，以最大程度降低延迟，并向前端提供流畅的实时反馈。

1.  **记忆/缓存检索 (L1/L2 -> L0/L1)**:
    -   **长期记忆检索**: **规划类Agent** 启动后，首先会从 **MySQL** 的 `user_memories` 和 `user_summaries` 表中拉取与当前用户和任务相关的长期记忆（用户画像、偏好、习惯等）。这些检索到的长期记忆被加载到当前任务在 Redis 的 Hash 中，成为会话期间的 **L1 短期记忆**。
    -   **工具调用缓存检索**: 在调用任何外部工具（如高德地图API）之前，引擎会根据**附录B**中的缓存键规范，先在Redis中查询是否存在有效的缓存结果。如果命中，则直接使用缓存，避免重复的API调用。

2.  **实时状态更新 (L0)**:
    -   AI引擎中的各个Agent（规划、搜索、分析等）在执行过程中，会产生大量的中间状态和日志。
    -   这些信息，包括对业务有意义的步骤 (`business_steps_log`)、模型的原始思考链 (`raw_model_trace`)、当前的进度百分比等，都会被实时地 `HSET` 到 Redis 的任务Hash中。
    -   这构成了 **L0 即时记忆**，它的生命周期仅限于当前任务的单个执行步骤。
    -   **智能决策**: Agent不仅仅是调用工具，更是在执行过程中进行复杂的智能决策。例如，`TripPlanningAgent`会基于用户偏好、行程天数等因素，从大量候选中**精选**出少量核心景点，并**优化游览顺序**。这些决策的中间结果（如候选列表、推荐理由）都会被记录在Redis的Hash中。
    -   **工具调用与缓存写入**: 如果缓存未命中，引擎会调用外部API。获取到结果后，会根据**附录B**的规范，将结果写入Redis缓存并设置合理的TTL。
    -   **交互确认**: 对于关键决策点（如最终去哪些景点），系统可以设计为暂停执行，并通过SSE向客户端推送一个`interactive_choice_required`事件，将选择权交给用户。
        -   此时，引擎暂停，等待用户通过一个专门的接口（如 `POST /v1/tasks/{task_id}/continue`）提交选择。
        -   **任务协调器**接收到用户的选择后，更新Redis中的任务状态，并唤醒对应的Agent继续执行。
        -   这个交互环节是**可插拔**的，在不需要用户干预的场景下可以被跳过，实现全自动规划。

3.  **实时进度反馈**:
    -   一个独立的SSE（Server-Sent Events）服务（由**任务协调器**管理）负责监控 Redis 中任务 Hash 的变化。
    -   一旦检测到内容更新，它会立刻将最新的进度信息通过流式连接推送到车端，供UI展示。
    -   **此阶段完全不涉及对 MySQL 和 MongoDB 的任何读写**，确保了核心规划链路的最高性能。

### 阶段三：任务结束 (归档与记忆沉淀)

当 AI 引擎成功完成任务并生成最终结果后，**任务协调器**进入收尾阶段，将"作战室"的成果进行永久封存和学习。

1.  **数据归档 (Archiving)**:
    -   **从Redis读取**: **任务协调器**执行 `HGETALL`，从 Redis 中一次性取出任务 Hash 的所有数据，这包含了从开始到结束的完整执行记录。
    -   **更新MongoDB**: **任务协调器**执行一次 `updateOne` 操作，用从Redis获取到的完整数据填充在第一阶段创建的"骨架"文档。同时，将文档状态更新为 `'SUCCESS'` 或 `'FAILED'`。至此，一份详尽的、可供分析和调试的"黑匣子"日志已永久保存在 `ai_interaction_logs` 中。
    -   **更新MySQL**: **任务协调器**执行 `UPDATE` 操作，更新 `ai_planning_sessions` 表中的对应任务状态，完成任务状态的闭环。
    -   **清理Redis**: **任务协调器**执行 `DEL` 命令，删除对应的任务 Hash，释放内存资源。

2.  **记忆沉淀 (Learning)**:
    -   **记忆生成Agent** 会分析本次交互的完整日志（特别是用户的输入和最终选择）。
    -   如果识别出新的、有价值的用户偏好或事实，**记忆评估Agent** 会对其进行打分。
    -   对于高价值的记忆，系统会将其 `INSERT` 或 `UPDATE` 到 **MySQL** 的 `user_memories` 表中，形成新的长期记忆。
    -   同时，可能会触发对 `user_summaries` 表中用户画像摘要的更新。

这个闭环流程确保了系统在提供高性能实时体验的同时，也构建了一个能够持续学习和进化的、安全可靠的长期记忆系统。

---

## 异常处理机制

为保证系统的稳定性和用户体验，设计了多层级的异常处理机制：

### 1. 任务执行超时 (主动监控)
- **机制**: AutoPilot AI引擎内部会对每个任务启动一个独立的计时器（例如，总时长不超过5分钟）。
- **处理**: 如果任务执行时间超出阈值，引擎会主动中断当前所有操作，将任务失败的信号报告给**任务协调器**，并附上`'TIMEOUT'`的错误信息。随后，协调器立即触发**数据归档**流程，将失败状态和日志持久化，并通知用户。

### 2. 工具调用失败与重试 (弹性容错)
- **机制**: 在调用外部API（如高德地图）时，如果遇到可恢复的错误（如网络波动、服务器临时5xx错误），系统不会立即失败。
- **处理**: 引擎会内置一套重试逻辑（例如，最多重试3次，采用指数退避策略增加重试间隔）。如果在所有重试后依然失败，则判定该步骤无法完成，整个任务失败。`tool_calls_log`中会详细记录每次的尝试与失败信息。

### 3. 用户主动取消
- **机制**: 系统需提供一个API端点（如 `DELETE /v1/tasks/{task_id}`），允许客户端主动请求取消一个正在进行中的任务。
- **处理**:
    1.  **任务协调器**接收到请求后，会向AI引擎发送一个"停止"信号。
    2.  引擎收到信号后，优雅地终止当前计算。
    3.  任务状态在Redis、MySQL、MongoDB中由**任务协调器**统一更新为 `'CANCELED'`。
    4.  最后清理Redis资源，并关闭与客户端的SSE连接。

### 4. Redis TTL自动回收 (被动安全网)
- **机制**: 这是防止系统因未知错误（如引擎进程意外崩溃）导致内存泄漏的最后一道防线。每个任务在Redis中创建的Hash Key都会被设置一个较长的过期时间（TTL，如30分钟）。
- **处理**: 如果一个任务的Key因为超时被Redis自动删除，一个后台的监控服务（可以基于Redis的Keyspace Notifications实现）会捕获到这个事件。随后，该服务会去更新MySQL和MongoDB中对应任务的状态为 `'FAILED_ORPHANED'`，确保所有系统的数据最终保持一致，并记录下这次意外。

---

## 附录A: Redis任务Hash结构详解

`task:{task_id}` 是一个 Redis Hash，作为任务执行期间的"实时白板"，其包含的字段和示例如下：

| 字段名 (Field Name) | 类型 (Type) | 描述 |
| :--- | :--- | :--- |
| **`task_id`** | `String` | 任务的唯一标识符。 |
| **`user_id`** | `String` | 发起任务的用户ID。 |
| **`status`** | `String` | **核心状态字段**。枚举值包括: `'PROCESSING'`, `'WAITING_FOR_INPUT'`, `'SUCCESS'`, `'FAILED'`, `'CANCELED'`。 |
| **`created_at`** | `Unix Timestamp` | 任务创建时间的Unix时间戳 (秒)。 |
| **`updated_at`** | `Unix Timestamp` | Hash内容最后更新时间的Unix时间戳 (秒)。 |
| **`progress`** | `Integer` | 任务进度的百分比，如 `10`, `50`, `90`。 |
| **`current_step_message`**| `String` | 当前执行步骤的简短描述，用于UI实时展示，如 "正在为您规划景点..."。 |
| **`raw_user_query`** | `String` | 用户未经处理的原始输入。 |
| **`parsed_intent`** | `JSON String`| LLM解析后的结构化意图，包含目的地、时间、偏好等。 |
| **`user_profile_snapshot`**| `JSON String`| 任务开始时从MySQL获取的用户画像和记忆快照。<br/>**安全提示**: 对于包含个人身份信息(PII)的字段，应考虑在存入缓存前进行脱敏或加密，或仅缓存与本次任务强相关的非敏感标签和偏好。 |
| **`llm_reasoning_trace`**| `JSON String`| (调试用) 记录LLM决策过程的思考链（Chain of Thought）。 |
| **`tool_calls_log`** | `JSON String`| 一个JSON数组，记录所有工具调用的历史，包括参数、重试次数和最终结果摘要。 |
| **`poi_candidates`** | `JSON String`| (决策前) 景点精选前的完整候选POI列表。 |
| **`selected_pois`** | `JSON String`| (决策后) 经过LLM智能精选后，最终确定的核心POI列表。 |
| **`interactive_choice_request`** | `JSON String`| 当 `status` 为 `'WAITING_FOR_INPUT'` 时，此字段存储需要用户确认的信息，如 `{"message": "为您推荐了故宫，是否确认？", "options": [...]}`。|
| **`user_choice_response`** | `JSON String`| 用户在交互环节做出的选择。 |
| **`final_result`** | `JSON String`| 当任务成功时，存储最终生成的完整、结构化的行程JSON。 |
| **`error_message`** | `String` | 当任务失败时，存储详细的错误信息，包括错误类型 (如 'TIMEOUT', 'TOOL_FAILURE') 和具体内容。 |

## 附录B: Redis缓存键设计规范

为了高效地利用Redis进行工具调用结果的缓存，特制定本规范。

### 1. 缓存键生成规则

所有外部工具的调用结果缓存，应遵循统一的命名约定：

**`cache:tool:{tool_name}:{params_hash}`**

-   **`cache:tool:`**: 固定的命名空间前缀，用于区分其他Redis数据。
-   **`{tool_name}`**: 工具的唯一名称，例如 `amap_maps_geo` 或 `amap_maps_weather`。
-   **`{params_hash}`**: 对工具的所有确定性输入参数（通常是一个字典）进行规范化（如按key排序）后，计算其MD5或SHA1哈希值。这确保了对于完全相同的请求，能生成唯一的、定长的哈希值。

**示例**:
-   **调用**: `amap_maps_geo(address="北京故宫")`
-   **参数字典**: `{"address": "北京故宫"}`
-   **规范化后的JSON**: `'{"address": "北京故宫"}'`
-   **MD5哈希**: `md5('{"address": "北京故宫"}')` -> `b1156417205b93a5573434b077a94c1c`
-   **最终缓存键**: `cache:tool:amap_maps_geo:b1156417205b93a5573434b077a94c1c`

### 2. 建议的TTL (Time-To-Live)

不同的API数据时效性不同，应设置差异化的TTL（生存时间）。

| 工具/数据类型 | 建议TTL (秒) | 建议TTL (可读) | 理由 |
| :--- | :--- | :--- | :--- |
| 地理编码 (`maps_geo`) | `864000` | 10天 | 地理位置信息非常稳定，变化频率极低。 |
| 天气查询 (`maps_weather`) | `14400` | 4小时 | 天气预报在短期内有效，更新频率较高。 |
| POI搜索/详情 (`maps_search`) | `86400` | 24小时 | POI信息（如评分、营业时间）可能会变化，但一天内的缓存是合理的。 |
| 路线规划 (`maps_direction_*`)| `7200` | 2小时 | 交通路况具有实时性，缓存时间不宜过长。 |
| 实时交通态势 | `300` | 5分钟 | 实时性要求最高的数据。 |
