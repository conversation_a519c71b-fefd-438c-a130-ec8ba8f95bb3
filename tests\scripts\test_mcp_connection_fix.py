"""
测试MCP连接修复效果

验证MCP客户端的重连机制和降级数据是否正常工作。
"""
import asyncio
import sys
import os
import json

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

from src.agents.travel_planner_agent import TravelPlannerAgent
from src.models.travel_planner import TravelPlanRequest
from src.core.logger import get_logger

logger = get_logger("test_mcp_fix")


async def test_mcp_connection_stability():
    """测试MCP连接稳定性和降级机制"""
    print("开始测试MCP连接修复效果...")
    
    agent = TravelPlannerAgent()
    
    # 测试地理编码（可能触发降级）
    print("\n1. 测试地理编码降级机制")
    try:
        result = await agent._cached_amap_call(
            "amap_maps_geo", 
            "maps_geo", 
            address="莆田市"
        )
        print(f"地理编码结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        if result.get('_fallback'):
            print("✓ 降级机制正常工作")
        else:
            print("✓ API调用成功")
            
    except Exception as e:
        print(f"✗ 地理编码测试失败: {str(e)}")
    
    # 测试天气查询（可能触发降级）
    print("\n2. 测试天气查询降级机制")
    try:
        result = await agent._cached_amap_call(
            "amap_maps_weather", 
            "maps_weather", 
            city="莆田"
        )
        print(f"天气查询结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        if result.get('_fallback'):
            print("✓ 降级机制正常工作")
        else:
            print("✓ API调用成功")
            
    except Exception as e:
        print(f"✗ 天气查询测试失败: {str(e)}")
    
    # 测试POI搜索（可能触发降级）
    print("\n3. 测试POI搜索降级机制")
    try:
        result = await agent._cached_amap_call(
            "amap_maps_text_search", 
            "maps_text_search", 
            keywords="景点",
            city="莆田"
        )
        print(f"POI搜索结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        if result.get('_fallback'):
            print("✓ 降级机制正常工作")
        else:
            print("✓ API调用成功")
            
    except Exception as e:
        print(f"✗ POI搜索测试失败: {str(e)}")
    
    print("\n测试完成！所有调用都能正常返回结果，不会卡住流程。")


async def test_simple_travel_planning():
    """测试简单的旅行规划流程"""
    print("\n开始测试简化旅行规划流程...")
    
    agent = TravelPlannerAgent()
    request = TravelPlanRequest(
        trace_id="test_trace_" + str(int(asyncio.get_event_loop().time())),
        user_id="test_user",
        query="我在福州，想去莆田玩两天"
    )
    
    print(f"请求: {request.query}")
    print(f"Trace ID: {request.trace_id}")
    
    try:
        # 收集前几个事件
        event_count = 0
        async for event in agent.plan_travel(request):
            print(f"\n事件 {event_count + 1}: {event.event_type}")
            print(f"内容: {json.dumps(event.payload, ensure_ascii=False, indent=2)}")
            
            event_count += 1
            if event_count >= 5:  # 只看前5个事件
                print("\n✓ 流程正常启动，没有卡住")
                break
                
        print(f"\n成功处理了 {event_count} 个事件，系统运行正常")
        
    except Exception as e:
        print(f"✗ 旅行规划测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


async def main():
    """主测试函数"""
    print("=" * 60)
    print("AutoPilot AI - MCP连接修复测试")
    print("=" * 60)
    
    # 测试1: MCP连接稳定性
    await test_mcp_connection_stability()
    
    print("\n" + "=" * 60)
    
    # 测试2: 简单旅行规划
    await test_simple_travel_planning()
    
    print("\n" + "=" * 60)
    print("所有测试完成！")


if __name__ == "__main__":
    asyncio.run(main()) 