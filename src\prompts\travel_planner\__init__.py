# Travel Planner Prompts Module 

"""
AutoPilot AI - 旅行规划 Prompt 管理

专门针对旅行规划 Agent 的 Prompt 模板管理类，
集成原有的意图理解、住宿决策、POI评分等各种 Prompt。
"""

from typing import Dict, Any, Optional, List
from datetime import datetime
import logging

from ..template_manager import PromptTemplateManager, PromptContext

logger = logging.getLogger(__name__)


class TravelPlannerPrompts:
    """旅行规划专用 Prompt 管理器"""
    
    def __init__(self, template_manager: PromptTemplateManager):
        """
        初始化旅行规划 Prompt 管理器
        
        Args:
            template_manager: 全局模板管理器实例
        """
        self.template_manager = template_manager
        self.logger = logger
    
    def render_intent_extraction_prompt(
        self, 
        user_query: str, 
        current_datetime: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        渲染意图理解和实体提取 Prompt
        
        Args:
            user_query: 用户查询内容
            current_datetime: 当前日期时间
            **kwargs: 额外的上下文变量
            
        Returns:
            渲染后的 Prompt 文本
        """
        # 如果没有提供当前时间，自动生成
        if not current_datetime:
            now = datetime.now()
            weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
            weekday = weekdays[now.weekday()]
            current_datetime = f"{now.strftime('%Y年%m月%d日')} {weekday}"
        
        context = PromptContext(
            user_query=user_query,
            current_time=current_datetime,
            custom_data=kwargs
        )
        
        try:
            return self.template_manager.render_template_with_schema(
                template_name="travel_planner/intent_extraction.md",
                context=context,
                schema_name="extract_travel_intent"
            )
        except Exception as e:
            self.logger.error(f"渲染意图提取 Prompt 失败: {str(e)}")
            # 返回硬编码的后备 Prompt
            return self._get_fallback_intent_prompt(user_query, current_datetime)
    
    def render_accommodation_decision_prompt(
        self,
        context: PromptContext,
        **kwargs
    ) -> str:
        """
        渲染住宿需求分析和决策 Prompt
        
        Args:
            context: 包含用户画像、提取实体等信息的上下文
            **kwargs: 额外的上下文变量
            
        Returns:
            渲染后的 Prompt 文本
        """
        try:
            return self.template_manager.render_template(
                template_name="travel_planner/accommodation_decision.md",
                context=context,
                **kwargs
            )
        except Exception as e:
            self.logger.error(f"渲染住宿决策 Prompt 失败: {str(e)}")
            return self._get_fallback_accommodation_prompt(context)
    
    def render_poi_scoring_prompt(
        self,
        context: PromptContext,
        poi_candidates: List[Dict[str, Any]],
        **kwargs
    ) -> str:
        """
        渲染 POI 综合评分 Prompt
        
        Args:
            context: 上下文信息
            poi_candidates: POI 候选列表
            **kwargs: 额外的上下文变量
            
        Returns:
            渲染后的 Prompt 文本
        """
        try:
            return self.template_manager.render_template_with_schema(
                template_name="travel_planner/poi_scoring.md",
                context=context,
                schema_name="poi_scoring_result",
                poi_candidates=poi_candidates,
                **kwargs
            )
        except Exception as e:
            self.logger.error(f"渲染 POI 评分 Prompt 失败: {str(e)}")
            return self._get_fallback_poi_scoring_prompt(context, poi_candidates)
    
    def render_weather_analysis_prompt(
        self,
        context: PromptContext,
        weather_data: Dict[str, Any],
        **kwargs
    ) -> str:
        """
        渲染天气影响分析 Prompt
        
        Args:
            context: 上下文信息
            weather_data: 天气数据
            **kwargs: 额外的上下文变量
            
        Returns:
            渲染后的 Prompt 文本
        """
        try:
            return self.template_manager.render_template(
                template_name="travel_planner/weather_analysis.md",
                context=context,
                weather_data=weather_data,
                **kwargs
            )
        except Exception as e:
            self.logger.error(f"渲染天气分析 Prompt 失败: {str(e)}")
            return self._get_fallback_weather_analysis_prompt(context, weather_data)
    
    def render_itinerary_optimization_prompt(
        self,
        context: PromptContext,
        scored_pois: List[Dict[str, Any]],
        **kwargs
    ) -> str:
        """
        渲染行程优化编排 Prompt
        
        Args:
            context: 上下文信息
            scored_pois: 评分后的 POI 列表
            **kwargs: 额外的上下文变量
            
        Returns:
            渲染后的 Prompt 文本
        """
        try:
            return self.template_manager.render_template_with_schema(
                template_name="travel_planner/itinerary_optimization.md",
                context=context,
                schema_name="optimized_itinerary",
                scored_pois=scored_pois,
                **kwargs
            )
        except Exception as e:
            self.logger.error(f"渲染行程优化 Prompt 失败: {str(e)}")
            return self._get_fallback_itinerary_optimization_prompt(context, scored_pois)
    
    def render_memory_extraction_prompt(
        self,
        context: PromptContext,
        task_execution_log: Dict[str, Any],
        **kwargs
    ) -> str:
        """
        渲染用户记忆提取 Prompt
        
        Args:
            context: 上下文信息
            task_execution_log: 任务执行日志
            **kwargs: 额外的上下文变量
            
        Returns:
            渲染后的 Prompt 文本
        """
        try:
            return self.template_manager.render_template_with_schema(
                template_name="travel_planner/memory_extraction.md",
                context=context,
                schema_name="extracted_memories",
                task_execution_log=task_execution_log,
                **kwargs
            )
        except Exception as e:
            self.logger.error(f"渲染记忆提取 Prompt 失败: {str(e)}")
            return self._get_fallback_memory_extraction_prompt(context, task_execution_log)
    
    def render_budget_analysis_prompt(
        self,
        context: PromptContext,
        itinerary_data: Dict[str, Any],
        **kwargs
    ) -> str:
        """
        渲染预算分析 Prompt
        
        Args:
            context: 上下文信息
            itinerary_data: 行程数据
            **kwargs: 额外的上下文变量
            
        Returns:
            渲染后的 Prompt 文本
        """
        try:
            return self.template_manager.render_template_with_schema(
                template_name="travel_planner/budget_analysis.md",
                context=context,
                schema_name="budget_estimation",
                itinerary_data=itinerary_data,
                **kwargs
            )
        except Exception as e:
            self.logger.error(f"渲染预算分析 Prompt 失败: {str(e)}")
            return self._get_fallback_budget_analysis_prompt(context, itinerary_data)
    
    # === 后备 Prompt 方法（保持与原代码兼容）===
    
    def _get_fallback_intent_prompt(self, user_query: str, current_datetime: str) -> str:
        """意图提取的后备 Prompt（硬编码版本）"""
        return f"""
请分析用户的旅行规划需求，提取关键信息：

系统当前时间：{current_datetime}
用户查询：{user_query}
出行方式：开车（固定）

请返回 JSON 格式的提取结果，包含：
- destination: 目的地
- start_date: 出发日期
- end_date: 结束日期  
- days: 出行天数
- people_count: 出行人数
- budget_range: 预算范围
- transportation: 交通方式（固定为"drive"）
- accommodation_preference: 住宿偏好
- special_requirements: 特殊需求
- interests: 兴趣偏好
"""
    
    def _get_fallback_accommodation_prompt(self, context: PromptContext) -> str:
        """住宿决策的后备 Prompt（硬编码版本）"""
        return f"""
基于用户画像和行程信息，分析住宿需求：

用户查询：{getattr(context, 'user_query', '未知')}
预算范围：{getattr(context, 'budget_range', '中等')}
出行人数：{getattr(context, 'people_count', 1)}人

请推荐合适的住宿类型和预算分配。
"""
    
    def _get_fallback_poi_scoring_prompt(
        self, 
        context: PromptContext, 
        poi_candidates: List[Dict[str, Any]]
    ) -> str:
        """POI评分的后备 Prompt（硬编码版本）"""
        return f"""
对以下 POI 进行综合评分，考虑用户偏好：

用户查询：{getattr(context, 'user_query', '未知')}
POI 候选：{len(poi_candidates)} 个

请为每个 POI 提供 1-10 分的评分和推荐理由。
"""
    
    def _get_fallback_weather_analysis_prompt(
        self, 
        context: PromptContext, 
        weather_data: Dict[str, Any]
    ) -> str:
        """天气分析的后备 Prompt（硬编码版本）"""
        return f"""
分析天气对行程的影响：

目的地：{getattr(context, 'destination', '未知')}
天气状况：{weather_data.get('weather', '未知')}

请提供行程建议和注意事项。
"""
    
    def _get_fallback_itinerary_optimization_prompt(
        self, 
        context: PromptContext, 
        scored_pois: List[Dict[str, Any]]
    ) -> str:
        """行程优化的后备 Prompt（硬编码版本）"""
        return f"""
基于评分结果优化行程安排：

出行天数：{getattr(context, 'days', '未知')}天
评分景点：{len(scored_pois)} 个

请生成详细的日程安排，包含时间、地点、活动内容。
"""
    
    def _get_fallback_memory_extraction_prompt(
        self, 
        context: PromptContext, 
        task_execution_log: Dict[str, Any]
    ) -> str:
        """记忆提取的后备 Prompt（硬编码版本）"""
        return f"""
从任务执行过程中提取用户偏好记忆：

用户ID：{getattr(context, 'user_id', '未知')}
任务类型：旅行规划

请分析用户的偏好模式，提取可学习的记忆点。
"""
    
    def _get_fallback_budget_analysis_prompt(
        self, 
        context: PromptContext, 
        itinerary_data: Dict[str, Any]
    ) -> str:
        """预算分析的后备 Prompt（硬编码版本）"""
        return f"""
分析行程预算构成：

预算范围：{getattr(context, 'budget_range', '中等')}
行程天数：{getattr(context, 'days', '未知')}天

请提供详细的预算分析和建议。
""" 