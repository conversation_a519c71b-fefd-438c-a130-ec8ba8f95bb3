---
description:
globs:
alwaysApply: false
---
# Agent 模块化重构指南

本项目正在进行一项重要的架构重构，目标是将单体的 `travel_planner_agent.py` 拆分为一个模块化的、可复用的多Agent架构。

## 核心规划文档

所有重构工作应严格遵循以下两个核心规划文档：

1.  **详细模块化方案**: `[Agent_Modularization_Plan.md](mdc:doc/progress/Agent_Modularization_Plan.md)`
    -   此文档包含了最终的目标架构、详细的模块拆分方案、类定义、实施步骤和验收标准。**这是本次重构最主要的参考文档。**

2.  **总体架构路线图**: `[Architecture_Refactoring_Plan.md](mdc:doc/progress/Architecture_Refactoring_Plan.md)`
    -   此文档将 Agent 模块化（阶段4）置于更宏大的项目重构路线图中，有助于理解其上下文。

## 目标架构概览

重构的核心是将 `src/agents/travel_planner_agent.py` 的功能拆分到以下结构中：

### 1. 通用 Agent 基础设施 (`src/agents/common/`)

- **目的**: 存放所有 Agent 可 100% 复用的组件。
- **`base/`**: 包含 `AgentBase` 和 `WorkflowAgent` 等基类。
- **`workflow/`**: 包含 `EventEmitter`, `StateManager` 等工作流组件。
- **`services/`**: 封装对 Redis, 数据库, LLM 的调用。
- **`utils/`**: 存放通用的工具函数，如验证器、错误处理器。

### 2. 旅行规划专用模块 (`src/agents/travel_planner/`)

- **`travel_planner_agent.py`**: 重构后的主 Agent 类，应精简为 **400行以内** 的协调器。
- **`core/`**: 核心业务逻辑。
    -   `intent_processor.py`: 意图理解。
    -   `poi_analyzer.py`: POI 分析与评分。
    -   `itinerary_composer.py`: 行程编排。
    -   `user_profiler.py`: 用户画像处理。
    -   `memory_manager.py`: 记忆管理。
- **`workflow/`**: 工作流管理。
    -   `phase_manager.py`: 管理四阶段工作流。
    -   `parallel_executor.py`: 管理并行工具调用。
- **`adapters/`**: 外部服务适配器。
    -   `amap_adapter.py`: 高德地图适配器。
    -   `weather_adapter.py`: 天气服务适配器。

## 重构关键指令

- **遵循单一职责原则**: 每个模块只做一件事。
- **优先实现 `common` 目录**: 这是实现复用性的基础。
- **逐步迁移**: 按照 `Agent_Modularization_Plan.md` 中规划的实施步骤，分阶段进行代码迁移和重构。
- **保持测试**: 在迁移过程中，应同步创建或修改单元测试和集成测试，确保功能稳定。

此规则旨在作为重构过程中的高级向导。在实施具体模块时，请务必详细阅读 `[Agent_Modularization_Plan.md](mdc:doc/progress/Agent_Modularization_Plan.md)` 中对应的章节。
