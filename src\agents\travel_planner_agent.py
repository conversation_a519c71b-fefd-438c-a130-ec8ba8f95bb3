"""
旅行规划Agent核心实现

基于PRD需求实现的智能旅行规划Agent，支持意图理解、工具规划、并行执行等功能。
"""
import asyncio
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, AsyncGenerator
import re

from src.models.travel_planner import (
    TravelPlanRequest, TravelItinerary, AgentState, StreamEvent,
    EventType, ThinkingCategory, ThinkingStepPayload, ToolCallPayload,
    ToolResultPayload, POIInfo, DailyPlan, TripSummary, WeatherInfo,
    Location, MapInfo, BudgetEstimation, BudgetBreakdown, UserProfile
)
from src.tools.amap_mcp_client import get_amap_client
import asyncio
from src.core.logger import get_logger
from src.core.llm_manager import LLMManager
from src.core.config import get_settings

# 导入MySQL CRUD系统
from src.models.mysql_crud import (
    user_crud, user_memory_crud, user_summary_crud,
    ai_planning_session_crud, itinerary_crud
)
from src.models.mysql_models import DhUserProfile
from src.database.mongodb_client import get_mongo_client
from src.database.mysql_client import get_db
from src.database.redis_client import get_redis_client, TaskState, TaskStep

# 导入原子化工具
# 导入新的 Prompt 管理系统
from src.prompts import get_travel_planner_prompts, PromptContext

# 导入缓存管理系统
from src.core.cache_manager import get_cache_manager, CacheManager

logger = get_logger("travel_planner_agent")


class TravelPlannerAgent:
    """旅行规划Agent核心类"""
    
    def __init__(self):
        """初始化旅行规划Agent"""
        self.logger = logger
        self.settings = get_settings()
        self.llm_manager = LLMManager()
        
        # 初始化LLM客户端
        self.reasoning_llm = self.llm_manager.get_client("reasoning")
        self.basic_llm = self.llm_manager.get_client("basic")
        
        # 初始化Redis客户端（延迟初始化）
        self._redis_client = None
        
        # 初始化 Prompt 管理器
        self.prompts = get_travel_planner_prompts()
        
        # 初始化缓存管理器（延迟初始化）
        self._cache_manager = None
        
    async def _get_redis_client(self):
        """获取Redis客户端实例"""
        if self._redis_client is None:
            self._redis_client = await get_redis_client()
        return self._redis_client
        
    async def _get_cache_manager(self) -> CacheManager:
        """获取缓存管理器实例"""
        if self._cache_manager is None:
            self._cache_manager = await get_cache_manager()
        return self._cache_manager
    
    async def _cached_amap_call(self, tool_name: str, method_name: str, **kwargs):
        """
        带缓存的高德地图API调用（支持降级）
        
        Args:
            tool_name: 工具名称，用于缓存键生成
            method_name: AmapMCPClient的方法名
            **kwargs: 方法参数
            
        Returns:
            API调用结果，失败时返回降级数据
        """
        cache_manager = await self._get_cache_manager()
        
        # 先尝试从缓存获取
        cached_result = await cache_manager.get(tool_name, kwargs)
        if cached_result is not None:
            self.logger.info(f"缓存命中: {tool_name}")
            return cached_result
        
        # 缓存未命中，尝试调用API
        try:
            amap_client = await get_amap_client()
            method = getattr(amap_client, method_name)
            
            # 设置调用超时
            result = await asyncio.wait_for(method(**kwargs), timeout=30.0)
            
            # 将结果写入缓存
            await cache_manager.set(tool_name, kwargs, result)
            self.logger.info(f"API调用成功并缓存: {tool_name}")
            
            return result
            
        except asyncio.TimeoutError:
            self.logger.warning(f"API调用超时: {tool_name}, 使用降级数据")
            return self._get_fallback_data(tool_name, **kwargs)
            
        except ConnectionError as e:
            self.logger.warning(f"MCP连接失败: {tool_name}, 使用降级数据, 错误: {str(e)}")
            return self._get_fallback_data(tool_name, **kwargs)
            
        except Exception as e:
            self.logger.error(f"API调用失败: {tool_name}, 使用降级数据, 错误: {str(e)}")
            return self._get_fallback_data(tool_name, **kwargs)
    
    def _get_fallback_data(self, tool_name: str, **kwargs) -> Dict[str, Any]:
        """
        获取降级数据，确保流程可以继续进行
        
        Args:
            tool_name: 工具名称
            **kwargs: 原始参数
            
        Returns:
            降级数据
        """
        if tool_name == "amap_maps_geo":
            # 地理编码降级：根据地名返回大概坐标
            address = kwargs.get('address', '北京市')
            fallback_locations = {
                '北京': {'location': '116.407526,39.90403'},
                '上海': {'location': '121.473701,31.230416'},
                '广州': {'location': '113.264434,23.129162'},
                '深圳': {'location': '114.085947,22.547'},
                '杭州': {'location': '120.153576,30.287459'},
                '成都': {'location': '104.065735,30.659462'},
                '西安': {'location': '108.948024,34.263161'},
                '武汉': {'location': '114.298572,30.584355'},
                '重庆': {'location': '106.504962,29.533155'},
                '天津': {'location': '117.190182,39.125596'},
                '南京': {'location': '118.767413,32.041544'},
                '苏州': {'location': '120.619585,31.299379'},
                '青岛': {'location': '120.355173,36.082982'},
                '大连': {'location': '121.618622,38.91459'},
                '厦门': {'location': '118.11022,24.490474'},
                '福州': {'location': '119.306239,26.075302'},
                '昆明': {'location': '102.712251,25.040609'},
                '长沙': {'location': '113.00415,28.19409'},
                '郑州': {'location': '113.665412,34.757975'},
                '石家庄': {'location': '114.502461,38.045474'},
                '济南': {'location': '117.000923,36.675807'},
                '哈尔滨': {'location': '126.657717,45.773225'},
                '沈阳': {'location': '123.429096,41.796767'},
                '长春': {'location': '125.3245,43.886841'},
                '太原': {'location': '112.549248,37.857014'},
                '南昌': {'location': '115.892151,28.676493'},
                '贵阳': {'location': '106.713478,26.578343'},
                '兰州': {'location': '103.823557,36.058039'},
                '银川': {'location': '106.27872,38.46637'},
                '西宁': {'location': '101.778916,36.623178'},
                '乌鲁木齐': {'location': '87.617733,43.792818'},
                '拉萨': {'location': '91.132212,29.660361'},
                '海口': {'location': '110.35004,20.018971'},
                '三亚': {'location': '109.511909,18.252847'},
                '呼和浩特': {'location': '111.670801,40.818311'},
                '莆田': {'location': '119.007558,25.431011'}
            }
            
            # 匹配城市名
            for city, coords in fallback_locations.items():
                if city in address:
                    return {
                        'geocodes': [coords],
                        'status': '1',
                        'info': 'OK',
                        'infocode': '10000',
                        '_fallback': True
                    }
            
            # 默认返回北京坐标
            return {
                'geocodes': [{'location': '116.407526,39.90403'}],
                'status': '1',
                'info': 'OK',
                'infocode': '10000',
                '_fallback': True
            }
            
        elif tool_name == "amap_maps_weather":
            # 天气查询降级：返回温和天气
            return {
                'lives': [{
                    'city': kwargs.get('city', '北京'),
                    'weather': '晴',
                    'temperature': '22',
                    'winddirection': '北',
                    'windpower': '≤3',
                    'humidity': '55',
                    'reporttime': '2023-06-26 15:30:00',
                    'temperature_float': '22.0',
                    'humidity_float': '55.0'
                }],
                'status': '1',
                'info': 'OK',
                '_fallback': True
            }
            
        elif tool_name in ["amap_maps_text_search", "amap_maps_around_search"]:
            # POI搜索降级：返回通用POI
            keywords = kwargs.get('keywords', '景点')
            return {
                'pois': [{
                    'id': 'fallback_001',
                    'name': f'{keywords}推荐地点',
                    'type': '景点景区',
                    'location': '116.407526,39.90403',
                    'address': '暂无具体地址',
                    'tel': '',
                    'rating': '4.5',
                    'tag': keywords,
                    'indoor_map': '0',
                    'photos': []
                }],
                'status': '1',
                'info': 'OK',
                'count': '1',
                '_fallback': True
            }
            
        else:
            # 其他工具的通用降级
            return {
                'status': '1',
                'info': 'OK',
                'result': 'fallback_data',
                '_fallback': True
            }
    
    async def _initialize_task_in_redis(self, request: TravelPlanRequest) -> TaskState:
        """在Redis中初始化任务状态"""
        redis_client = await self._get_redis_client()
        
        # 创建任务状态
        task_state = await redis_client.create_task_state(
            task_id=request.trace_id,
            user_id=request.user_id,
            original_query=request.query
        )
        
        # 更新状态为运行中
        await redis_client.update_task_state(
            request.trace_id,
            status="running",
            current_step="init"
        )
        
        self.logger.info(f"任务已在Redis中初始化: {request.trace_id}")
        return task_state
        
    async def _record_task_step(self, task_id: str, step_data: dict) -> TaskStep:
        """记录任务步骤到Redis"""
        redis_client = await self._get_redis_client()
        
        step = TaskStep(
            step_id=str(uuid.uuid4()),
            agent_name="TravelPlannerAgent",
            tool_name=step_data.get("tool_name"),
            tool_input=step_data.get("tool_input"),
            status=step_data.get("status", "completed"),
            input=step_data.get("input"),
            output=step_data.get("output"),
            timestamp=datetime.now(),
            business_step_log=step_data.get("business_step_log"),
            raw_model_trace=step_data.get("raw_model_trace")
        )
        
        await redis_client.record_task_step(task_id, step)
        return step
        
    async def _update_task_progress(self, task_id: str, progress: float, current_step: str):
        """更新任务进度"""
        redis_client = await self._get_redis_client()
        await redis_client.update_task_progress(task_id, progress, current_step)
        
    async def _store_l1_memory(self, task_id: str, memory_key: str, memory_data: Any):
        """存储L1短期记忆"""
        redis_client = await self._get_redis_client()
        await redis_client.store_l1_memory(task_id, memory_key, memory_data)
        
    async def _get_l1_memory(self, task_id: str, memory_key: str) -> Optional[Any]:
        """获取L1短期记忆"""
        redis_client = await self._get_redis_client()
        return await redis_client.get_l1_memory(task_id, memory_key)
        
    async def _update_business_step_log(self, task_id: str, step_log: str):
        """更新业务步骤日志，兼容新Redis接口"""
        redis_client = await self._get_redis_client()
        # 简单规则映射
        if "开始" in step_log:
            step_id, status = "start", "init"
        elif "完成" in step_log:
            step_id, status = "end", "completed"
        elif "失败" in step_log:
            step_id, status = "error", "failed"
        else:
            step_id, status = "other", step_log
        await redis_client.update_business_step_log(task_id, step_id, status)
        
    async def _update_raw_model_trace(self, task_id: str, model_trace: str):
        """更新模型原始思考链，兼容新Redis接口"""
        redis_client = await self._get_redis_client()
        # 将字符串转换为字典格式
        trace_dict = {
            "input": model_trace,
            "output": "",
            "reasoning": ""
        }
        await redis_client.update_raw_model_trace(task_id, trace_dict)
        
    async def plan_travel(
        self, 
        request: TravelPlanRequest
    ) -> AsyncGenerator[StreamEvent, None]:
        """
        四阶段旅行规划工作流
        
        Args:
            request: 旅行规划请求
            
        Yields:
            StreamEvent: 流式事件
        """
        # 在Redis中初始化任务状态
        try:
            task_state = await self._initialize_task_in_redis(request)
        except Exception as e:
            self.logger.error(f"Redis任务初始化失败: {str(e)}")
            # 继续执行，不阻塞主流程
        
        # 初始化Agent状态
        state = AgentState(
            trace_id=request.trace_id,
            user_id=request.user_id,
            original_query=request.query,
            current_step="init"
        )
        
        try:
            # 更新任务进度到Redis
            await self._update_task_progress(request.trace_id, 0.0, "init")
            await self._update_business_step_log(request.trace_id, "开始旅行规划任务")
            
            # Phase 1: 意图理解与个性化融合
            await self._update_task_progress(request.trace_id, 10.0, "phase1_intent_personalization")
            async for event in self._phase1_intent_and_personalization(state):
                yield event
                
            # Phase 2: 动态工具规划与并行执行
            await self._update_task_progress(request.trace_id, 30.0, "phase2_parallel_tools")
            async for event in self._phase2_parallel_tool_execution(state):
                yield event
                
            # Phase 3: 数据综合与智能决策
            await self._update_task_progress(request.trace_id, 70.0, "phase3_reasoning")
            async for event in self._phase3_data_synthesis_and_reasoning(state):
                yield event
                
            # Phase 4: 结构化结果生成与记忆存储
            await self._update_task_progress(request.trace_id, 90.0, "phase4_result_generation")
            async for event in self._phase4_result_generation_and_storage(state):
                yield event
                
            # 任务完成
            await self._update_task_progress(request.trace_id, 100.0, "completed")
            await self._update_business_step_log(request.trace_id, "旅行规划任务完成")
            
        except Exception as e:
            self.logger.error(f"旅行规划过程中发生错误: {str(e)}", extra={
                "trace_id": state.trace_id,
                "user_id": request.user_id,
                "error": str(e)
            })
            
            # 更新任务状态为失败
            try:
                await self._update_task_progress(request.trace_id, -1.0, "failed")
                await self._update_business_step_log(request.trace_id, f"任务失败: {str(e)}")
            except Exception as redis_error:
                self.logger.error(f"更新Redis失败状态时出错: {str(redis_error)}")
            
            # 发送错误事件
            error_event = StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.ERROR,
                payload={
                    "error_message": f"规划过程中发生错误: {str(e)}",
                    "error_type": type(e).__name__
                }
            )
            yield error_event
    
    async def _phase1_intent_and_personalization(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """Phase 1: 意图理解与个性化融合"""
        state.current_step = "phase1_intent_personalization"
        
        # 发送思考开始事件
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_START,
            payload={"phase": "Phase 1: 意图理解与个性化融合"}
        )
        
        # 步骤1: 意图理解和实体提取
        async for event in self._understand_intent(state):
            yield event
            
        # 步骤2: 获取用户画像
        async for event in self._get_user_profile(state):
            yield event
            
        # 步骤3: 构建个性化查询指令
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_STEP,
            payload=ThinkingStepPayload(
                category=ThinkingCategory.OTHER,
                content="已融合用户画像，准备开始动态工具规划"
            ).model_dump()
        )
    
    async def _phase2_parallel_tool_execution(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """Phase 2: 动态工具规划与四层并行执行"""
        state.current_step = "phase2_parallel_tools"
        
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_STEP,
            payload=ThinkingStepPayload(
                category=ThinkingCategory.OTHER,
                content="开始四层并行工具调用，收集旅行规划所需信息"
            ).model_dump()
        )
        
        # 第一层：基础信息层
        async for event in self._layer1_basic_info(state):
            yield event
            
        # 第二层：核心POI信息层 
        async for event in self._layer2_core_poi_info(state):
            yield event
            
        # 第三层：路线与辅助信息层
        async for event in self._layer3_route_and_auxiliary(state):
            yield event
            
        # 第四层：深度信息挖掘层
        async for event in self._layer4_deep_mining(state):
            yield event
    
    async def _phase3_data_synthesis_and_reasoning(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """Phase 3: 数据综合与智能决策"""
        state.current_step = "phase3_reasoning"
        
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_STEP,
            payload=ThinkingStepPayload(
                category=ThinkingCategory.OTHER,
                content="开始智能决策分析，综合所有信息生成最优行程方案"
            ).model_dump()
        )
        
        # 天气影响分析
        async for event in self._analyze_weather_impact(state):
            yield event
            
        # POI综合评分
        async for event in self._score_and_rank_pois(state):
            yield event
            
        # 智能行程编排
        async for event in self._orchestrate_itinerary(state):
            yield event
            
        # 预算分析
        async for event in self._estimate_budget(state):
            yield event
    
    async def _phase4_result_generation_and_storage(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """Phase 4: 结构化结果生成与记忆存储"""
        state.current_step = "phase4_generation_storage"
        
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_STEP,
            payload=ThinkingStepPayload(
                category=ThinkingCategory.OTHER,
                content="正在生成最终行程并保存用户记忆"
            ).model_dump()
        )

        # 生成最终行程
        async for event in self._generate_final_itinerary(state):
            yield event
            
        # 保存用户记忆 上一步的时候前端就停止接受，不执行后续了
        async for event in self._save_user_memory(state):
            yield event
            
    async def _understand_intent(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """使用原子化Function Call工具进行意图理解和实体提取"""
        state.current_step = "intent_understanding"
        
        # 发送思考开始事件
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_START,
            payload={"step": "意图理解和实体提取"}
        )
        
        # 获取当前时间
        current_time = datetime.now()
        current_date = current_time.strftime("%Y年%m月%d日")
        current_weekday = current_time.strftime("%A")
        weekday_cn = {
            "Monday": "周一", "Tuesday": "周二", "Wednesday": "周三", 
            "Thursday": "周四", "Friday": "周五", "Saturday": "周六", "Sunday": "周日"
        }
        current_weekday_cn = weekday_cn.get(current_weekday, current_weekday)
        current_datetime = f"{current_date} {current_weekday_cn}"
        
        # 构建基础提示词
        base_prompt = f"""
请分析用户的旅行规划需求，提取关键信息：

系统当前时间：{current_datetime}
用户查询：{state.original_query}
出行方式：开车（固定）

请严格按照JSON Schema提取以下信息：
1. 目的地城市（如果用户没有明确提及，请标记为null）
2. 出行天数（数字类型，如果没有明确提及则为null）
3. 出行时间（具体日期或相对时间，如"下周末"）
4. 出行人数和类型（如情侣、家庭、朋友等）
5. 预算范围（经济型/中等/豪华）
6. 兴趣偏好（如文化、美食、自然风光等）
7. 特殊需求（如无障碍、亲子友好等）

注意：天数字段必须是数字类型，出行方式固定为"driving"。
"""
        
        try:
            # 使用新的 Prompt 管理系统生成提示词
            formatted_prompt = self.prompts.render_intent_extraction_prompt(
                user_query=state.original_query,
                current_datetime=current_datetime
            )
            
            self.logger.info(f"[DEBUG] 使用新 Prompt 系统生成的提示词长度: {len(formatted_prompt)}")
            
            # 调用推理模型进行意图理解
            response = await self.reasoning_llm.chat_completion(
                messages=[{"role": "user", "content": formatted_prompt}],
                temperature=0.1
            )
            
            content = response['choices'][0]['message']['content']
            self.logger.info(f"[DEBUG] LLM响应内容: {content}")
            
            # 尝试提取JSON - 更严格的解析
            try:
                # 先尝试直接解析（假设LLM直接返回JSON）
                extracted_entities = json.loads(content.strip())
                self.logger.info(f"[DEBUG] 直接JSON解析成功: {extracted_entities}")
            except json.JSONDecodeError:
                # 如果直接解析失败，尝试从文本中提取JSON
                json_match = re.search(r'\{.*?\}', content, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    self.logger.info(f"[DEBUG] 从文本提取的JSON: {json_str}")
                    extracted_entities = json.loads(json_str)
                    self.logger.info(f"[DEBUG] 提取JSON解析成功: {extracted_entities}")
                else:
                    raise ValueError("LLM响应中未找到有效的JSON格式")
            
            # 验证和清理提取的实体数据
            extracted_entities = self._validate_extracted_entities(extracted_entities)
            state.extracted_entities = extracted_entities
            
            # 发送思考步骤事件
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.THINKING_STEP,
                payload=ThinkingStepPayload(
                    category=ThinkingCategory.TRAVEL_OBJECT,
                    content=f"已提取用户需求：目的地={extracted_entities.get('destination')}, "
                           f"天数={extracted_entities.get('days')}, "
                           f"偏好={extracted_entities.get('preferences')}"
                ).model_dump()
            )
            
            self.logger.info(f"[SUCCESS] 意图理解完成，提取的实体: {extracted_entities}")
                
        except Exception as e:
            self.logger.error(f"[ERROR] 意图理解失败: {str(e)}", exc_info=True)
            
            # 设置默认值，确保后续处理不会出错
            default_entities = {
                "destination": None, 
                "origin": None,
                "days": 1,  # 默认1天，确保是整数
                "travel_time": None,
                "travelers": None,
                "budget": None,
                "preferences": None,
                "special_needs": None,
                "transport_mode": "driving"
            }
            state.extracted_entities = default_entities
            self.logger.info(f"[FALLBACK] 使用默认实体: {default_entities}")
            
            # 发送错误提示事件
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.THINKING_STEP,
                payload=ThinkingStepPayload(
                    category=ThinkingCategory.OTHER,
                    content=f"意图理解遇到问题，使用默认设置: {str(e)}"
                ).model_dump()
            )
    
    def _validate_extracted_entities(self, entities: Dict[str, Any]) -> Dict[str, Any]:
        """验证和清理提取的实体数据，兼容多种字段名格式"""
        validated = {}
        
        # 验证目的地 - 兼容多种字段名
        destination = (entities.get("destination") or 
                      entities.get("destination_city") or 
                      entities.get("目的地城市"))
        validated["destination"] = destination if destination != "" else None
            
        # 验证起点 - 兼容多种字段名
        origin = (entities.get("origin") or 
                 entities.get("出发点") or 
                 entities.get("起点"))
        validated["origin"] = origin if origin != "" else None
        
        # 验证天数（确保是整数） - 兼容多种字段名
        days = (entities.get("days") or 
               entities.get("travel_days") or  # 添加对travel_days字段的支持
               entities.get("出行天数") or 
               entities.get("天数"))
        if days is not None:
            try:
                validated["days"] = int(days)
            except (ValueError, TypeError):
                validated["days"] = 3  # 默认3天
        else:
            validated["days"] = 3
            
        # 验证出行时间 - 兼容多种字段名
        travel_time = (entities.get("travel_time") or 
                      entities.get("出行时间"))
        validated["travel_time"] = travel_time
        
        # 验证出行人员 - 兼容多种字段名
        travelers = (entities.get("travelers") or 
                    entities.get("出行人数和类型") or 
                    entities.get("出行人员"))
        validated["travelers"] = travelers
        
        # 验证预算 - 兼容多种字段名
        budget = (entities.get("budget") or 
                 entities.get("预算范围"))
        validated["budget"] = budget
        
        # 验证偏好 - 兼容多种字段名
        preferences = (entities.get("preferences") or 
                      entities.get("兴趣偏好") or 
                      entities.get("preferences_list"))
        validated["preferences"] = preferences if preferences is not None else []
        
        # 验证特殊需求 - 兼容多种字段名
        special_needs = (entities.get("special_needs") or 
                        entities.get("特殊需求"))
        validated["special_needs"] = special_needs if special_needs is not None else []
        
        # 验证出行方式 - 兼容多种字段名
        transport_mode = (entities.get("transport_mode") or 
                         entities.get("出行方式"))
        validated["transport_mode"] = transport_mode if transport_mode else "driving"
        
        self.logger.info(f"[DEBUG] 字段映射后的验证结果: {validated}")
        return validated

    def _extract_preferences_from_memories(self, memories: List) -> Dict[str, Any]:
        """从用户记忆中提取偏好信息"""
        preferences = {}
        
        # 统计记忆类型偏好
        memory_types = {}
        destinations = []
        activities = []
        
        for memory in memories:
            # 提取记忆类型
            if hasattr(memory, 'memory_type') and memory.memory_type:
                memory_types[memory.memory_type] = memory_types.get(memory.memory_type, 0) + 1
            
            # 提取内容中的偏好关键词
            if hasattr(memory, 'content') and memory.content:
                content = memory.content.lower()
                
                # 检测活动偏好
                if any(word in content for word in ['博物馆', '历史', '文化']):
                    activities.append('文化历史')
                if any(word in content for word in ['美食', '小吃', '餐厅']):
                    activities.append('美食')
                if any(word in content for word in ['自然', '风景', '山', '海']):
                    activities.append('自然风光')
                if any(word in content for word in ['购物', '商场', '买']):
                    activities.append('购物')
                if any(word in content for word in ['夜生活', '酒吧', '夜市']):
                    activities.append('夜生活')
                    
                # 检测预算偏好
                if any(word in content for word in ['经济', '便宜', '省钱']):
                    preferences['budget_style'] = '经济型'
                elif any(word in content for word in ['豪华', '高端', '奢华']):
                    preferences['budget_style'] = '豪华型'
                else:
                    preferences['budget_style'] = '中等'
        
        # 设置最喜欢的活动类型
        if activities:
            activity_count = {}
            for activity in activities:
                activity_count[activity] = activity_count.get(activity, 0) + 1
            preferences['favorite_activities'] = [k for k, v in sorted(activity_count.items(), key=lambda x: x[1], reverse=True)][:3]
        
        return preferences
    
    def _extract_preferences_from_itineraries(self, itineraries: List) -> Dict[str, Any]:
        """从历史行程中提取偏好信息"""
        preferences = {}
        
        destinations = []
        trip_durations = []
        
        for itinerary in itineraries:
            # 提取目的地偏好
            if hasattr(itinerary, 'city_name') and itinerary.city_name:
                destinations.append(itinerary.city_name)
            
            # 提取行程天数偏好
            if hasattr(itinerary, 'total_days') and itinerary.total_days:
                trip_durations.append(itinerary.total_days)
        
        # 设置偏好的目的地类型
        if destinations:
            preferences['preferred_destinations'] = list(set(destinations))[:5]
        
        # 设置偏好的行程天数
        if trip_durations:
            avg_duration = sum(trip_durations) / len(trip_durations)
            preferences['preferred_trip_duration'] = round(avg_duration)
        
        print("preferences", preferences)
        return preferences

    async def _get_user_profile(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """获取用户画像和记忆"""
        state.current_step = "get_user_profile"
        
        # 发送思考步骤事件
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_STEP,
            payload=ThinkingStepPayload(
                category=ThinkingCategory.OTHER,
                content="正在获取用户画像和历史记忆"
            ).model_dump()
        )
        
        try:
            # 首先尝试从Redis L1记忆中获取用户画像
            cached_profile = await self._get_l1_memory(state.trace_id, "user_profile")
            if cached_profile:
                self.logger.info(f"从Redis L1记忆获取到用户画像缓存: {state.user_id}")
                state.user_profile = UserProfile(**cached_profile)
                
                # 发送用户画像事件
                yield StreamEvent(
                    event_id=str(uuid.uuid4()),
                    trace_id=state.trace_id,
                    event_type=EventType.THINKING_STEP,
                    payload=ThinkingStepPayload(
                        category=ThinkingCategory.OTHER,
                        content="已从缓存获取用户画像"
                    ).model_dump()
                )
                return
            
            # 从数据库获取用户记忆和画像
            async with get_db() as db:
                # 获取用户记忆
                user_memories = await user_memory_crud.get_by_user(db, user_id=state.user_id)
                
                # 获取用户画像总结
                user_summary = await user_summary_crud.get_by_user(db, user_id=state.user_id)
                
                # 获取历史行程
                itineraries = await itinerary_crud.get_by_user(db, user_id=state.user_id)
                
                # 构建完整的用户画像
                preferences = {}
                tags = []
                
                # 从用户记忆中提取偏好
                if user_memories:
                    memory_preferences = self._extract_preferences_from_memories(user_memories)
                    preferences.update(memory_preferences)
                    
                    # 从记忆关键词中提取标签
                    for memory in user_memories:
                        if hasattr(memory, 'keywords') and memory.keywords:
                            if isinstance(memory.keywords, list):
                                tags.extend(memory.keywords)
                            elif isinstance(memory.keywords, str):
                                import json
                                try:
                                    keywords = json.loads(memory.keywords)
                                    if isinstance(keywords, list):
                                        tags.extend(keywords)
                                except:
                                    tags.append(memory.keywords)
                
                # 从用户画像总结中获取信息
                if user_summary:
                    if hasattr(user_summary, 'keywords') and user_summary.keywords:
                        if isinstance(user_summary.keywords, list):
                            tags.extend(user_summary.keywords)
                    
                    if hasattr(user_summary, 'travel_style') and user_summary.travel_style:
                        preferences['travel_style'] = user_summary.travel_style
                        
                    if hasattr(user_summary, 'budget_preference') and user_summary.budget_preference:
                        preferences['budget_preference'] = user_summary.budget_preference
                
                # 从历史行程中提取偏好
                if itineraries:
                    itinerary_preferences = self._extract_preferences_from_itineraries(itineraries)
                    preferences.update(itinerary_preferences)
                
                # 创建用户画像
                user_profile = UserProfile(
                    user_id=state.user_id,
                    preferences=preferences,
                    tags=list(set(tags))[:10],  # 去重并限制数量
                    travel_style=preferences.get('travel_style', '休闲'),
                    budget_preference=preferences.get('budget_preference', '中等'),
                    favorite_activities=preferences.get('favorite_activities', []),
                    preferred_destinations=preferences.get('preferred_destinations', []),
                    preferred_trip_duration=preferences.get('preferred_trip_duration', 3)
                )
                
                state.user_profile = user_profile
                state.user_memories = user_memories
                
                # 将用户画像存储到Redis L1记忆中
                await self._store_l1_memory(
                    state.trace_id, 
                    "user_profile", 
                    user_profile.model_dump()
                )
                
                # 将用户记忆存储到Redis L1记忆中
                if user_memories:
                    # 将SQLAlchemy对象转换为可序列化的字典
                    serializable_memories = []
                    for memory in user_memories:
                        memory_dict = {}
                        for key, value in memory.__dict__.items():
                            # 跳过SQLAlchemy内部属性
                            if not key.startswith('_'):
                                # 处理datetime对象
                                if hasattr(value, 'isoformat'):
                                    memory_dict[key] = value.isoformat()
                                # 处理其他可序列化的对象
                                elif isinstance(value, (str, int, float, bool, list, dict, type(None))):
                                    memory_dict[key] = value
                                else:
                                    # 对于其他对象，转换为字符串
                                    memory_dict[key] = str(value)
                        serializable_memories.append(memory_dict)
                    
                    await self._store_l1_memory(
                        state.trace_id, 
                        "user_memories", 
                        serializable_memories
                    )
                
                # 记录任务步骤
                await self._record_task_step(state.trace_id, {
                    "tool_name": "user_profile_retrieval",
                    "status": "completed",
                    "input": f"user_id: {state.user_id}",
                    "output": f"获取到用户画像，偏好数量: {len(preferences)}, 标签数量: {len(tags)}",
                    "business_step_log": f"成功获取用户{state.user_id}的画像和历史记忆"
                })
                
                # 发送用户画像事件
                yield StreamEvent(
                    event_id=str(uuid.uuid4()),
                    trace_id=state.trace_id,
                    event_type=EventType.THINKING_STEP,
                    payload=ThinkingStepPayload(
                        category=ThinkingCategory.OTHER,
                        content=f"已获取用户画像，包含{len(preferences)}个偏好，{len(tags)}个标签"
                    ).model_dump()
                )
                
        except Exception as e:
            self.logger.error(f"获取用户画像失败: {str(e)}", exc_info=True)
            
            # 记录错误步骤
            await self._record_task_step(state.trace_id, {
                "tool_name": "user_profile_retrieval",
                "status": "failed",
                "input": f"user_id: {state.user_id}",
                "output": f"错误: {str(e)}",
                "business_step_log": f"获取用户画像失败: {str(e)}"
            })
            
            # 创建默认用户画像
            state.user_profile = UserProfile(
                user_id=state.user_id,
                preferences={},
                tags=[],
                travel_style="休闲",
                budget_preference="中等"
            )
    
    async def _geolocate_destination(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """地理定位"""
        state.current_step = "geolocation"
        self.logger.info(f"[DEBUG] 开始地理定位")
        
        destination = state.extracted_entities.get("destination")
        self.logger.info(f"[DEBUG] 提取的目的地: {destination}")
        if not destination:
            self.logger.info(f"[DEBUG] 目的地为空，跳过地理定位")
            return
            
        # 发送工具调用事件
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.TOOL_CALL,
            payload=ToolCallPayload(
                tool_name="maps_geo",
                parameters={"address": destination}
            ).model_dump()
        )
        
        try:
            # 使用缓存的API调用
            geo_result = await self._cached_amap_call(
                tool_name="amap_maps_geo",
                method_name="maps_geo",
                address=destination
            )
            
            state.tool_results["geolocation"] = geo_result
            
            # 发送工具结果事件
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.TOOL_RESULT,
                payload=ToolResultPayload(
                    tool_name="maps_geo",
                    result_summary=f"成功定位{destination}",
                    success=True
                ).model_dump()
            )
            
        except Exception as e:
            self.logger.error(f"地理定位失败: {str(e)}")
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.TOOL_RESULT,
                payload=ToolResultPayload(
                    tool_name="maps_geo",
                    result_summary=f"定位失败: {str(e)}",
                    success=False
                ).model_dump()
            )
            
    async def _get_weather_forecast(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """获取天气预报"""
        state.current_step = "weather_forecast"
        
        destination = state.extracted_entities.get("destination")
        if not destination:
            return
            
        # 发送工具调用事件
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.TOOL_CALL,
            payload=ToolCallPayload(
                tool_name="maps_weather",
                parameters={"city": destination}
            ).model_dump()
        )
        
        try:
            # 使用缓存的API调用
            weather_result = await self._cached_amap_call(
                tool_name="amap_maps_weather",
                method_name="maps_weather",
                city=destination
            )
            
            state.tool_results["weather"] = weather_result
            
            # 发送工具结果事件
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.TOOL_RESULT,
                payload=ToolResultPayload(
                    tool_name="maps_weather",
                    result_summary=f"获取{destination}天气预报成功",
                    success=True
                ).model_dump()
            )
            
        except Exception as e:
            self.logger.error(f"天气查询失败: {str(e)}")
            
    async def _search_and_recommend_pois(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """搜索和推荐POI"""
        state.current_step = "poi_search"
        
        destination = state.extracted_entities.get("destination")
        if not destination:
            return
            
        # 根据用户偏好搜索不同类型的POI
        poi_categories = ["景点", "美食", "酒店"]
        
        for category in poi_categories:
            # 发送工具调用事件
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.TOOL_CALL,
                payload=ToolCallPayload(
                    tool_name="maps_text_search",
                    parameters={"keywords": category, "city": destination}
                ).model_dump()
            )
            
            try:
                # 使用缓存的API调用
                search_result = await self._cached_amap_call(
                    tool_name="amap_maps_text_search",
                    method_name="maps_text_search",
                    keywords=category,
                    city=destination,
                    offset=10
                )
                
                state.tool_results[f"poi_{category}"] = search_result
                
                # 发送工具结果事件
                yield StreamEvent(
                    event_id=str(uuid.uuid4()),
                    trace_id=state.trace_id,
                    event_type=EventType.TOOL_RESULT,
                    payload=ToolResultPayload(
                        tool_name="maps_text_search",
                        result_summary=f"找到{category}相关POI",
                        success=True
                    ).model_dump()
                )
                
            except Exception as e:
                self.logger.error(f"POI搜索失败 ({category}): {str(e)}")
                
    async def _plan_itinerary(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """行程规划"""
        state.current_step = "itinerary_planning"
        
        # 发送规划步骤事件
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.PLANNING_STEP,
            payload={"step": "正在规划每日行程安排"}
        )
        
        # 这里实现具体的行程规划逻辑
        # 暂时创建示例行程
        days = state.extracted_entities.get("days", 3)
        
        # 确保days是整数类型，防止None + int错误
        if days is None or not isinstance(days, int):
            days = 3
            
        daily_plans = []
        
        for day in range(1, days + 1):
            daily_plan = DailyPlan(
                day=day,
                theme=f"第{day}天行程",
                pois=[]
            )
            daily_plans.append(daily_plan)
            
        state.tool_results["daily_plans"] = daily_plans
        
    async def _plan_routes(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """路线规划"""
        state.current_step = "route_planning"

        # 发送规划步骤事件
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.PLANNING_STEP,
            payload={"step": "正在规划最优路线"}
        )

        # 获取每日行程中的POI，规划路线
        daily_plans = state.tool_results.get("daily_plans", [])

        for daily_plan in daily_plans:
            if len(daily_plan.pois) > 1:
                # 为每日行程规划路线
                pois = daily_plan.pois
                for i in range(len(pois) - 1):
                    origin_poi = pois[i]
                    dest_poi = pois[i + 1]

                    if origin_poi.location and dest_poi.location:
                        try:
                            origin = f"{origin_poi.location.longitude},{origin_poi.location.latitude}"
                            destination = f"{dest_poi.location.longitude},{dest_poi.location.latitude}"

                            # 使用缓存的路线规划API调用
                            route_result = await self._cached_amap_call(
                                tool_name="amap_maps_direction_walking",
                                method_name="maps_direction_walking",
                                origin=origin,
                                destination=destination
                            )

                            # 存储路线信息
                            route_key = f"route_day_{daily_plan.day}_{i}"
                            state.tool_results[route_key] = route_result

                        except Exception as e:
                            self.logger.error(f"路线规划失败: {str(e)}")

        # 生成个人地图链接
        try:
            all_pois = []
            for daily_plan in daily_plans:
                for poi in daily_plan.pois:
                    if poi.location:
                        all_pois.append(f"{poi.location.longitude},{poi.location.latitude}")

            if all_pois:
                amap_client = await get_amap_client()
                pois_str = "|".join(all_pois)
                map_result = await amap_client.maps_schema_personal_map(
                    pois=pois_str,
                    name=f"{state.extracted_entities.get('destination', '旅行')}行程地图"
                )
                state.tool_results["personal_map"] = map_result

        except Exception as e:
            self.logger.error(f"生成个人地图失败: {str(e)}")

    async def _estimate_budget(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """预算估算"""
        state.current_step = "budget_estimation"

        # 发送规划步骤事件
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.PLANNING_STEP,
            payload={"step": "正在估算旅行预算"}
        )

        # 基于POI类型和数量估算预算
        days = state.extracted_entities.get("days", 3)
        
        # 确保days是整数类型，防止None * int错误
        if days is None or not isinstance(days, int):
            days = 3
            
        daily_plans = state.tool_results.get("daily_plans", [])

        # 预算估算逻辑
        accommodation_cost = days * 200  # 每晚住宿200元
        food_cost = days * 150  # 每天餐饮150元
        attraction_cost = 0
        transport_cost = days * 50  # 每天交通50元

        # 根据景点数量估算门票费用
        for daily_plan in daily_plans:
            for poi in daily_plan.pois:
                if poi.category == "景点":
                    attraction_cost += 50  # 平均门票50元

        breakdown = [
            BudgetBreakdown(category="住宿", amount=accommodation_cost),
            BudgetBreakdown(category="餐饮", amount=food_cost),
            BudgetBreakdown(category="景点门票", amount=attraction_cost),
            BudgetBreakdown(category="交通", amount=transport_cost)
        ]

        total_cost = accommodation_cost + food_cost + attraction_cost + transport_cost

        budget_estimation = BudgetEstimation(
            total_min=total_cost * 0.8,
            total_max=total_cost * 1.2,
            breakdown=breakdown
        )

        state.tool_results["budget"] = budget_estimation
        
    async def _generate_final_itinerary(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """使用原子化Function Call工具生成最终行程"""
        state.current_step = "final_generation"
        
        # 发送规划步骤事件
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.PLANNING_STEP,
            payload={"step": "正在生成最终行程JSON"}
        )
        
        # 验证并获取必需字段
        destination = state.extracted_entities.get("destination")
        days = state.extracted_entities.get("days", 3)
        
        # 如果目的地为空，使用默认值或从工具结果中推断
        if not destination:
            destination = self._infer_destination_from_results(state)
            
        # 确保days是有效的整数
        if not isinstance(days, int) or days <= 0:
            days = 3
            
        self.logger.info(f"[DEBUG] 生成最终行程 - 目的地: {destination}, 天数: {days}")
        
        try:
            # 构建完整的行程数据，符合Schema要求
            summary = TripSummary(
                title=f"{destination}{days}日游",
                days=days,
                destination_city=destination,
                tags=["智能规划", "开车出行"],
                description="AI智能规划的旅行行程"
            )
            
            # 确保daily_plans有正确的数据结构
            daily_plans = self._build_valid_daily_plans(state, days)
            
            final_itinerary = TravelItinerary(
                trace_id=state.trace_id,
                user_id=state.user_id,
                status="completed",
                raw_user_query=state.original_query,
                summary=summary,
                weather_forecast=state.tool_results.get("weather_forecast", []),
                daily_plans=daily_plans,
                budget_estimation=state.tool_results.get("budget")
            )
            
            # 验证最终行程是否符合Schema
            self._validate_final_itinerary(final_itinerary)
            
            state.final_itinerary = final_itinerary

            # 发送最终行程事件
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.FINAL_ITINERARY,
                payload=final_itinerary.model_dump()
            )
            
            self.logger.info(f"[SUCCESS] 最终行程生成完成，trace_id: {state.trace_id}")
            
        except Exception as e:
            self.logger.error(f"[ERROR] 生成最终行程失败: {str(e)}", exc_info=True)
            
            # 生成最小化的有效行程
            fallback_itinerary = self._create_fallback_itinerary(state, destination, days)
            state.final_itinerary = fallback_itinerary
            
            # 发送最终行程事件
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.FINAL_ITINERARY,
                payload=fallback_itinerary.model_dump()
            )
    
    def _infer_destination_from_results(self, state: AgentState) -> str:
        """从工具结果中推断目的地"""
        # 尝试从地理定位结果中获取
        geo_result = state.tool_results.get("geolocation")
        if geo_result and isinstance(geo_result, dict):
            # 从高德地理编码结果中提取城市名
            if "geocodes" in geo_result and len(geo_result["geocodes"]) > 0:
                return geo_result["geocodes"][0].get("city", "未知目的地")
        
        # 从用户查询中尝试提取地名
        query = state.original_query
        # 简单的地名提取逻辑
        import re
        city_pattern = r'([北京|上海|广州|深圳|杭州|南京|苏州|成都|重庆|西安|天津|青岛|大连|厦门|武汉|长沙|沈阳|哈尔滨|济南|昆明|贵阳|兰州|银川|西宁|拉萨|乌鲁木齐|呼和浩特|石家庄|太原|合肥|福州|南昌|郑州|长春|海口|三亚|桂林|丽江|九寨沟|黄山|泰山|华山|峨眉山|庐山|衡山|嵩山|恒山|五台山]|.*市|.*县|.*区)'
        match = re.search(city_pattern, query)
        if match:
            return match.group(1)
            
        return "目的地"
    
    def _build_valid_daily_plans(self, state: AgentState, days: int) -> List[DailyPlan]:
        """构建有效的每日行程计划"""
        existing_plans = state.tool_results.get("daily_plans", [])
        
        # 如果已有计划且数量正确，直接使用
        if existing_plans and len(existing_plans) == days:
            return existing_plans
            
        # 否则构建新的计划
        daily_plans = []
        for day in range(1, days + 1):
            # 从现有计划中获取对应天的数据，或创建默认计划
            existing_plan = None
            if existing_plans and len(existing_plans) >= day:
                existing_plan = existing_plans[day - 1]
                
            if existing_plan and hasattr(existing_plan, 'pois'):
                daily_plan = existing_plan
            else:
                # 创建默认的每日计划
                daily_plan = DailyPlan(
                    day=day,
                    theme=f"第{day}天行程",
                    pois=self._create_sample_pois(state, day)
                )
            
            daily_plans.append(daily_plan)
            
        return daily_plans
    
    def _create_sample_pois(self, state: AgentState, day: int) -> List[POIInfo]:
        """为指定天创建示例POI"""
        destination = state.extracted_entities.get("destination", "目的地")
        
        # 基于天数创建不同的POI
        sample_pois = []
        
        if day == 1:
            # 第一天：主要景点
            sample_pois.append(POIInfo(
                poi_instance_id=f"poi_{day}_1",
                name=f"{destination}标志性景点",
                category="景点",
                description="当地著名景点",
                estimated_duration_min=120
            ))
        else:
            # 其他天：其他类型POI
            sample_pois.append(POIInfo(
                poi_instance_id=f"poi_{day}_1",
                name=f"{destination}特色体验",
                category="景点",
                description="当地特色活动",
                estimated_duration_min=90
            ))
            
        return sample_pois
    
    def _validate_final_itinerary(self, itinerary: TravelItinerary) -> None:
        """验证最终行程是否符合Schema要求"""
        # 检查必需字段
        if not itinerary.trace_id:
            raise ValueError("trace_id不能为空")
        if not itinerary.user_id:
            raise ValueError("user_id不能为空")
        if not itinerary.summary.title:
            raise ValueError("行程标题不能为空")
        if not itinerary.summary.destination_city:
            raise ValueError("目的地城市不能为空")
        if itinerary.summary.days <= 0:
            raise ValueError("天数必须大于0")
            
        # 检查每日计划
        if not itinerary.daily_plans:
            raise ValueError("必须包含每日计划")
        
        for daily_plan in itinerary.daily_plans:
            if not isinstance(daily_plan.day, int) or daily_plan.day <= 0:
                raise ValueError(f"每日计划的天数必须是正整数: {daily_plan.day}")
                
        self.logger.info("[VALIDATION] 最终行程验证通过")
    
    def _create_fallback_itinerary(self, state: AgentState, destination: str, days: int) -> TravelItinerary:
        """创建最小化的有效行程作为后备方案"""
        summary = TripSummary(
            title=f"{destination}{days}日游",
            days=days,
            destination_city=destination,
            tags=["智能规划"],
            description="AI智能规划的旅行行程（简化版）"
        )
        
        # 创建最基本的每日计划
        daily_plans = []
        for day in range(1, days + 1):
            daily_plan = DailyPlan(
                day=day,
                theme=f"第{day}天",
                pois=[POIInfo(
                    poi_instance_id=f"fallback_poi_{day}",
                    name=f"{destination}游览",
                    category="景点"
                )]
            )
            daily_plans.append(daily_plan)
        
        return TravelItinerary(
            trace_id=state.trace_id,
            user_id=state.user_id,
            status="completed",
            raw_user_query=state.original_query,
            summary=summary,
            weather_forecast=[],
            daily_plans=daily_plans
        )

    async def _layer1_basic_info(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """第一层并行调用：基础信息层"""
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_STEP,
            payload=ThinkingStepPayload(
                category=ThinkingCategory.OTHER,
                content="第一层：并行获取地理定位和天气信息"
            ).model_dump()
        )
        
        destination = state.extracted_entities.get("destination")
        if not destination:
            self.logger.warning("目的地为空，跳过地理定位")
            return
            
        # 并行调用地理定位和天气查询
        tasks = []
        
        # 地理定位任务
        async def geolocate_task():
            async for event in self._geolocate_destination(state):
                yield event
                
        # 天气查询任务  
        async def weather_task():
            async for event in self._get_weather_forecast(state):
                yield event
        
        # 并行执行地理定位和天气查询
        import asyncio
        geolocate_gen = geolocate_task()
        weather_gen = weather_task()
        
        try:
            # 并行执行两个生成器
            async for event in geolocate_gen:
                yield event
            async for event in weather_gen:
                yield event
        except Exception as e:
            self.logger.error(f"第一层并行调用失败: {str(e)}")

    async def _layer2_core_poi_info(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """第二层并行调用：核心POI信息层"""
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_STEP,
            payload=ThinkingStepPayload(
                category=ThinkingCategory.ATTRACTION_RECOMMENDATION,
                content="第二层：并行搜索停车场、美食、景点、住宿等核心POI"
            ).model_dump()
        )
        
        destination = state.extracted_entities.get("destination")
        if not destination:
            return
            
        # 定义POI搜索类别和关键词
        poi_searches = [
            ("停车场", "停车场"),
            ("充电桩", "充电桩"), 
            ("美食", "美食"),
            ("景点", "景点"),
            ("酒店", "酒店")
        ]
        
        # 并行执行POI搜索
        import asyncio
        amap_client = await get_amap_client()
        
        for category, keywords in poi_searches:
            # 发送工具调用事件
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.TOOL_CALL,
                payload=ToolCallPayload(
                    tool_name="maps_text_search",
                    parameters={"keywords": keywords, "city": destination}
                ).model_dump()
            )
            
            try:
                search_result = await amap_client.maps_text_search(
                    keywords=keywords,
                    city=destination,
                    offset=10
                )
                
                state.tool_results[f"poi_{category}"] = search_result
                
                # 发送工具结果事件
                yield StreamEvent(
                    event_id=str(uuid.uuid4()),
                    trace_id=state.trace_id,
                    event_type=EventType.TOOL_RESULT,
                    payload=ToolResultPayload(
                        tool_name="maps_text_search",
                        result_summary=f"找到{len(search_result.get('pois', []))}个{category}POI",
                        success=True
                    ).model_dump()
                )
                
                # 对前3个POI获取详细信息
                pois = search_result.get('pois', [])[:3]
                for poi in pois:
                    poi_id = poi.get('id')
                    if poi_id:
                        try:
                            detail_result = await amap_client.maps_search_detail(id=poi_id)
                            state.tool_results[f"poi_detail_{poi_id}"] = detail_result
                        except Exception as e:
                            self.logger.error(f"获取POI详情失败 {poi_id}: {str(e)}")
                
            except Exception as e:
                self.logger.error(f"POI搜索失败 ({category}): {str(e)}")
                
    async def _layer3_route_and_auxiliary(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """第三层并行调用：路线与辅助信息层"""
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_STEP,
            payload=ThinkingStepPayload(
                category=ThinkingCategory.OTHER,
                content="第三层：规划路线和收集辅助信息"
            ).model_dump()
        )
        
        # 基于前面收集的POI信息进行路线规划
        # 这里可以实现具体的路线规划逻辑
        pass
        
    async def _layer4_deep_mining(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """第四层并行调用：深度信息挖掘层"""
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_STEP,
            payload=ThinkingStepPayload(
                category=ThinkingCategory.OTHER,
                content="第四层：个性化深度挖掘和应急信息收集"
            ).model_dump()
        )
        
        # 基于用户画像进行个性化搜索
        # 收集应急信息(医院、药店等)
        pass
        
    async def _analyze_weather_impact(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """分析天气对行程的影响"""
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_STEP,
            payload=ThinkingStepPayload(
                category=ThinkingCategory.OTHER,
                content="分析天气影响，调整室内外活动安排"
            ).model_dump()
        )
        
        weather_data = state.tool_results.get("weather")
        if weather_data:
            # 基于天气数据调整推荐策略
            # 如有雨天则推荐室内活动
            state.weather_analysis = {
                "has_rain": False,  # 实际应该解析天气数据
                "indoor_recommended": False,
                "suggestions": []
            }
            
    async def _score_and_rank_pois(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """POI综合评分和排序"""
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_STEP,
            payload=ThinkingStepPayload(
                category=ThinkingCategory.ATTRACTION_RECOMMENDATION,
                content="正在对POI进行综合评分和智能排序"
            ).model_dump()
        )
        
        # 收集所有POI并评分
        all_pois = []
        for key, value in state.tool_results.items():
            if key.startswith("poi_") and isinstance(value, dict):
                pois = value.get('pois', [])
                for poi in pois:
                    poi['category'] = key.replace('poi_', '')
                    poi['score'] = self._calculate_poi_score(poi, state)
                    all_pois.append(poi)
        
        # 如果有足够的 POI，使用新的 Prompt 系统进行智能评分
        if len(all_pois) > 5:
            try:
                # 构建上下文
                context = PromptContext(
                    user_id=state.user_id,
                    trace_id=state.trace_id,
                    extracted_entities=state.extracted_entities,
                    tool_results=state.tool_results,
                    user_profile=getattr(state, 'user_profile', None),
                    user_memories=getattr(state, 'user_memories', None)
                )
                
                # 生成 POI 评分 Prompt
                scoring_prompt = self.prompts.render_poi_scoring_prompt(
                    context=context,
                    poi_candidates=all_pois[:20]  # 限制数量避免 Prompt 过长
                )
                
                self.logger.info(f"[DEBUG] 使用新 Prompt 系统生成 POI 评分提示词")
                
                # 调用推理模型进行智能评分
                response = await self.reasoning_llm.chat_completion(
                    messages=[{"role": "user", "content": scoring_prompt}],
                    temperature=0.3
                )
                
                # 解析评分结果（这里可以进一步处理LLM的响应）
                content = response['choices'][0]['message']['content']
                self.logger.info(f"[DEBUG] LLM POI评分响应: {content[:200]}...")
                
            except Exception as e:
                self.logger.error(f"[ERROR] 使用新 Prompt 系统评分失败: {str(e)}")
                # 降级到原有的简单评分逻辑
                    
        # 按分数排序
        all_pois.sort(key=lambda x: x.get('score', 0), reverse=True)
        state.scored_pois = all_pois
        
    def _calculate_poi_score(self, poi: dict, state: AgentState) -> float:
        """基于用户画像和记忆计算POI综合得分"""
        score = 0.0
        
        # 基础评分权重 (30%)
        rating = float(poi.get('rating', 0))
        score += rating * 0.3
        
        # 用户画像偏好匹配 (40%)
        if hasattr(state, 'user_profile') and state.user_profile:
            profile_score = self._calculate_profile_match_score(poi, state.user_profile)
            score += profile_score * 0.4
        
        # 用户记忆匹配 (20%)
        if hasattr(state, 'user_memories') and state.user_memories:
            memory_score = self._calculate_memory_match_score(poi, state.user_memories)
            score += memory_score * 0.2
        
        # 类别权重调整 (10%)
        category = poi.get('category', '')
        if category == '景点':
            score += 0.5
        elif category == '美食':
            score += 0.3
        elif category == '酒店':
            score += 0.2
        elif category == '停车场':
            score += 0.1  # 必需但不是主要兴趣点
            
        return score
    
    def _calculate_profile_match_score(self, poi: dict, user_profile: UserProfile) -> float:
        """计算POI与用户画像的匹配分数"""
        match_score = 0.0
        
        poi_name = poi.get('name', '').lower()
        poi_type = poi.get('type', '').lower()
        poi_category = poi.get('category', '').lower()
        
        # 检查用户标签匹配
        for tag in user_profile.tags:
            tag_lower = tag.lower()
            if tag_lower in poi_name or tag_lower in poi_type:
                match_score += 1.0
        
        # 检查偏好活动匹配
        favorite_activities = user_profile.preferences.get('favorite_activities', [])
        for activity in favorite_activities:
            activity_lower = activity.lower()
            if '文化历史' in activity_lower and any(word in poi_name for word in ['博物馆', '历史', '文化', '古迹']):
                match_score += 2.0
            elif '美食' in activity_lower and any(word in poi_name for word in ['餐厅', '美食', '小吃', '菜']):
                match_score += 2.0
            elif '自然风光' in activity_lower and any(word in poi_name for word in ['公园', '山', '湖', '海', '风景']):
                match_score += 2.0
            elif '购物' in activity_lower and any(word in poi_name for word in ['商场', '市场', '购物']):
                match_score += 2.0
        
        # 检查预算偏好匹配
        budget_pref = user_profile.budget_preference
        if budget_pref == '经济型':
            # 经济型用户偏好评分相对较低但性价比高的地方
            if rating := poi.get('rating'):
                if 3.5 <= float(rating) <= 4.2:
                    match_score += 0.5
        elif budget_pref == '豪华型':
            # 豪华型用户偏好高评分场所
            if rating := poi.get('rating'):
                if float(rating) >= 4.5:
                    match_score += 1.0
        
        return min(match_score, 5.0)  # 限制最高分数
    
    def _calculate_memory_match_score(self, poi: dict, user_memories: List) -> float:
        """计算POI与用户记忆的匹配分数"""
        match_score = 0.0
        
        poi_name = poi.get('name', '').lower()
        poi_type = poi.get('type', '').lower()
        
        for memory in user_memories[:10]:  # 只检查最近的10条记忆
            if hasattr(memory, 'content') and memory.content:
                memory_content = memory.content.lower()
                
                # 检查记忆内容与POI的相似性
                common_keywords = self._extract_common_keywords(memory_content, poi_name + ' ' + poi_type)
                if common_keywords:
                    # 根据记忆置信度加权
                    confidence = getattr(memory, 'confidence_score', 0.5)
                    match_score += len(common_keywords) * confidence * 0.5
                
                # 检查记忆关键词匹配
                if hasattr(memory, 'keywords') and memory.keywords:
                    keywords = memory.keywords
                    if isinstance(keywords, str):
                        try:
                            import json
                            keywords = json.loads(keywords)
                        except:
                            keywords = [keywords]
                    
                    if isinstance(keywords, list):
                        for keyword in keywords:
                            if keyword.lower() in poi_name or keyword.lower() in poi_type:
                                confidence = getattr(memory, 'confidence_score', 0.5)
                                match_score += confidence * 1.0
        
        return min(match_score, 3.0)  # 限制最高分数
    
    def _extract_common_keywords(self, text1: str, text2: str) -> List[str]:
        """提取两个文本的共同关键词"""
        # 简单的关键词提取逻辑
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        # 过滤掉常见停用词
        stop_words = {'的', '是', '在', '有', '和', '与', '或', '但', '很', '非常', '一个', '这个', '那个'}
        words1 = words1 - stop_words
        words2 = words2 - stop_words
        
        # 只保留长度>=2的词
        words1 = {w for w in words1 if len(w) >= 2}
        words2 = {w for w in words2 if len(w) >= 2}
        
        return list(words1 & words2)
    
    async def _save_user_memory(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """保存本次规划的用户记忆到数据库"""

        state.current_step = "save_memory"
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_STEP,
            payload=ThinkingStepPayload(
                category=ThinkingCategory.OTHER,
                content="正在保存用户记忆到数据库"
            ).model_dump()
        )
        
        try:
            # 从Redis获取完整的任务执行记录
            redis_client = await self._get_redis_client()
            task_steps = await redis_client.get_task_steps(state.trace_id)
            business_logs = await redis_client.get_l1_memory(state.trace_id, "business_steps_log")
            
            async with get_db() as db:
                # 构建记忆内容
                destination = state.extracted_entities.get('destination', '未知')
                days = state.extracted_entities.get('days', 1)
                preferences = state.extracted_entities.get('preferences', [])
                
                # 从最终行程中提取关键信息
                memory_content = f"用户在{destination}进行了{days}天的旅行规划"
                keywords = [destination]
                
                if preferences:
                    memory_content += f"，偏好包括：{', '.join(preferences)}"
                    keywords.extend(preferences)
                
                # 从评分后的POI中提取热门类型
                if hasattr(state, 'scored_pois') and state.scored_pois:
                    top_categories = {}
                    for poi in state.scored_pois[:10]:  # 只看前10个高分POI
                        category = poi.get('category', '其他')
                        top_categories[category] = top_categories.get(category, 0) + 1
                    
                    if top_categories:
                        top_category = max(top_categories.items(), key=lambda x: x[1])[0]
                        memory_content += f"，主要关注{top_category}类型的POI"
                        keywords.append(top_category)
                
                # 创建记忆记录
                import json
                from datetime import datetime
                
                memory_data = {
                    'user_id': state.user_id,
                    'memory_content': memory_content,
                    'source_session_id': state.trace_id,
                    # 'memory_type': 'travel_planning',
                    # 'keywords': json.dumps(keywords, ensure_ascii=False),
                    'confidence': 0.8,  # 系统生成的记忆置信度较高
                    # 'source': 'ai_planning',
                    'created_at': datetime.now(),
                    'last_accessed': datetime.now()
                }
                
                # 保存记忆
                await user_memory_crud.create_memory(db, memory_data=memory_data)
                
                # 更新用户画像总结（如果不存在则创建）
                user_summary = await user_summary_crud.get_by_user(db, user_id=state.user_id)
                
                # 构建或更新兴趣标签
                current_interests = []
                if user_summary and hasattr(user_summary, 'keywords'):
                    if isinstance(user_summary.keywords, list):
                        current_interests = user_summary.keywords
                    elif isinstance(user_summary.keywords, str):
                        try:
                            current_interests = json.loads(user_summary.keywords)
                        except:
                            current_interests = [user_summary.keywords]
                
                # 添加新的兴趣标签
                for keyword in keywords:
                    if keyword not in current_interests:
                        current_interests.append(keyword)
                
                # 限制标签数量
                current_interests = [x for x in current_interests if x and isinstance(x, str) and x.strip()]
                current_interests = current_interests[:15]

                summary_data = {
                    'user_id': state.user_id,
                    # 'summary': json.dumps(current_interests, ensure_ascii=False),
                    'keywords': current_interests,
                    # 'travel_style': state.user_profile.travel_style if hasattr(state, 'user_profile') else '休闲',
                    # 'budget_preference': state.user_profile.budget_preference if hasattr(state, 'user_profile') else '中等',
                    'updated_at': datetime.now()
                }
                
                if user_summary:
                    # 更新现有画像
                    await user_summary_crud.update_summary(db, user_id=state.user_id, summary_data=summary_data)
                else:
                    # 创建新画像
                    await user_summary_crud.create_summary(db, summary_data=summary_data)
                
                #更新 ai_planning_sessions 表中的对应任务状态，完成任务状态的闭环
                # 保存AI规划会话记录
                session_data = {
                    # 'id': state.trace_id,
                    'user_id': state.user_id,
                    # 'session_id': state.trace_id,
                    'user_input': json.dumps(state.extracted_entities, ensure_ascii=False),
                    'status': 'SUCCESS',
                    'raw_llm_output': json.dumps(state.orchestrated_itinerary, ensure_ascii=False),
                    # 'final_itinerary_id': state.final_itinerary.trace_id,
                    # 'destination': destination,
                    # 'days': days,
                    'completed_at': datetime.now()
                }
                await ai_planning_session_crud.update_session(db, id=state.trace_id, session_data=session_data)
                
                # 构建MongoDB交互日志数据，包含Redis中的完整执行记录
                interaction_data = {
                    "status": "SUCCESS",
                    "final_output": json.dumps(state.orchestrated_itinerary, ensure_ascii=False),
                    "task_steps": [step.model_dump() for step in task_steps] if task_steps else [],
                    "business_logs": business_logs if business_logs else [],
                    "execution_summary": {
                        "total_steps": len(task_steps) if task_steps else 0,
                        "completed_steps": len([s for s in task_steps if s.status == 'completed']) if task_steps else 0,
                        "failed_steps": len([s for s in task_steps if s.status == 'failed']) if task_steps else 0,
                        "execution_time": (datetime.now() - task_steps[0].timestamp).total_seconds() if task_steps else 0
                    }
                }
                
                # 执行一次 updateOne 操作，用从Redis获取到的完整数据填充在第一阶段创建的"骨架"文档
                mongo_client = await get_mongo_client()
                await mongo_client.update_interaction_log(interaction_id=state.trace_id, interaction_data=interaction_data)
                
                # 清理Redis中的任务数据（可选，根据业务需求决定是否立即清理）
                # await redis_client.cleanup_task_data(state.trace_id)
                
                self.logger.info(f"[SUCCESS] 用户记忆保存完成: {state.user_id}")
                
                yield StreamEvent(
                    event_id=str(uuid.uuid4()),
                    trace_id=state.trace_id,
                    event_type=EventType.THINKING_STEP,
                    payload=ThinkingStepPayload(
                        category=ThinkingCategory.OTHER,
                        content=f"已保存记忆和更新用户画像：{len(keywords)}个关键词，{len(current_interests)}个兴趣标签"
                    ).model_dump()
                )
                
        except Exception as e:
            self.logger.error(f"[ERROR] 保存用户记忆失败: {str(e)}", exc_info=True)
            
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.THINKING_STEP,
                payload=ThinkingStepPayload(
                    category=ThinkingCategory.OTHER,
                    content=f"保存用户记忆时出现错误: {str(e)}"
                ).model_dump()
            )
        
    async def _orchestrate_itinerary(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """智能行程编排"""
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.PLANNING_STEP,
            payload={"step": "正在进行智能行程编排和时间优化"}
        )
        
        days = state.extracted_entities.get("days", 1)
        if days is None or not isinstance(days, int):
            days = 1
            
        # 基于评分后的POI生成每日行程
        scored_pois = getattr(state, 'scored_pois', [])
        
        daily_plans = []
        pois_per_day = max(1, len(scored_pois) // days)
        
        for day in range(1, days + 1):
            start_idx = (day - 1) * pois_per_day
            end_idx = start_idx + pois_per_day
            
            day_pois = scored_pois[start_idx:end_idx]
            
            # 转换为POIInfo对象
            poi_infos = []
            for poi in day_pois:
                # 从POI的经纬度字符串中解析
                lon, lat = (0, 0)
                location_str = poi.get('location', '')
                if ',' in location_str:
                    try:
                        lon, lat = map(float, location_str.split(','))
                    except ValueError:
                        pass # 保持默认值

                poi_info = POIInfo(
                    poi_instance_id=str(uuid.uuid4()),  # <--- FIX: 生成唯一的实例ID
                    poi_id=poi.get('id', ''),
                    name=poi.get('name', ''),
                    category=poi.get('category', '其他'),
                    rating=float(poi.get('rating', 0.0)) if poi.get('rating') else 0.0,
                    address=poi.get('address', ''),
                    location=Location(longitude=lon, latitude=lat),
                    estimated_duration_min=120,  # 默认2小时
                    description=poi.get('type', ''),
                    tips="建议提前预约"
                )
                poi_infos.append(poi_info)
                
            daily_plan = DailyPlan(
                day=day,
                theme=f"第{day}天行程",
                pois=poi_infos
            )
            daily_plans.append(daily_plan)
            
        state.tool_results["daily_plans"] = daily_plans
        
        # 将编排后的行程数据保存到state.orchestrated_itinerary
        orchestrated_data = {
            "destination": state.extracted_entities.get('destination', '未知'),
            "days": days,
            "daily_plans": [plan.model_dump() for plan in daily_plans],
            "total_pois": len(scored_pois),
            "orchestrated_at": datetime.now().isoformat()
        }
        
        state.orchestrated_itinerary = orchestrated_data
        
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.PLANNING_STEP,
            payload={"step": f"行程编排完成，生成{days}天行程，包含{len(scored_pois)}个POI"}
        )
