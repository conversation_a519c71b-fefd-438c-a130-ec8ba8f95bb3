"""
AutoPilot AI - Prompt 模板管理系统

这个模块提供了基于 Jinja2 的智能 Prompt 模板管理功能，
支持动态渲染、JSON Schema 集成和多语言模板。
"""

from .template_manager import PromptTemplateManager, PromptContext
from .travel_planner import TravelPlannerPrompts

__all__ = [
    "PromptTemplateManager",
    "PromptContext", 
    "TravelPlannerPrompts"
]

# 创建全局的 Prompt 管理器实例
_global_manager = None

def get_prompt_manager() -> PromptTemplateManager:
    """获取全局 Prompt 管理器实例"""
    global _global_manager
    if _global_manager is None:
        _global_manager = PromptTemplateManager()
    return _global_manager

def get_travel_planner_prompts() -> TravelPlannerPrompts:
    """获取旅行规划相关的 Prompt 实例"""
    return TravelPlannerPrompts(get_prompt_manager()) 