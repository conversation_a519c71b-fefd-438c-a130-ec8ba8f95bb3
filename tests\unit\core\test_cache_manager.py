"""
AutoPilot AI - 缓存管理器单元测试

测试 Redis 缓存管理器的核心功能，包括：
- 缓存键生成规范
- TTL配置策略
- 缓存读写操作
- 统计信息收集
- 错误处理机制
"""

import pytest
import json
import hashlib
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any

from src.core.cache_manager import CacheManager, CacheStats, cached_tool_call, get_cache_manager


class TestCacheManager:
    """缓存管理器测试类"""
    
    def setup_method(self):
        """每个测试方法前的setup"""
        self.cache_manager = CacheManager()
    
    def test_cache_key_generation(self):
        """测试缓存键生成规范"""
        # 测试基础参数
        params = {"address": "北京故宫"}
        cache_key = self.cache_manager._generate_cache_key("amap_maps_geo", params)
        
        # 验证缓存键格式
        assert cache_key.startswith("cache:tool:amap_maps_geo:")
        
        # 验证参数哈希值
        normalized_params = json.dumps(params, sort_keys=True, ensure_ascii=False)
        expected_hash = hashlib.md5(normalized_params.encode('utf-8')).hexdigest()
        expected_key = f"cache:tool:amap_maps_geo:{expected_hash}"
        
        assert cache_key == expected_key
    
    def test_cache_key_consistency(self):
        """测试缓存键的一致性"""
        params1 = {"address": "北京故宫", "city": "北京"}
        params2 = {"city": "北京", "address": "北京故宫"}  # 顺序不同
        
        key1 = self.cache_manager._generate_cache_key("amap_maps_geo", params1)
        key2 = self.cache_manager._generate_cache_key("amap_maps_geo", params2)
        
        # 验证参数顺序不影响键值
        assert key1 == key2
    
    def test_ttl_configuration(self):
        """测试TTL配置策略"""
        # 测试地理编码TTL（10天）
        ttl_geo = self.cache_manager._get_ttl("amap_maps_geo")
        assert ttl_geo == 864000  # 10 * 24 * 3600
        
        # 测试天气查询TTL（4小时）
        ttl_weather = self.cache_manager._get_ttl("amap_maps_weather")
        assert ttl_weather == 14400  # 4 * 3600
        
        # 测试POI搜索TTL（24小时）
        ttl_poi = self.cache_manager._get_ttl("amap_maps_text_search")
        assert ttl_poi == 86400  # 24 * 3600
        
        # 测试路线规划TTL（2小时）
        ttl_route = self.cache_manager._get_ttl("amap_maps_direction_walking")
        assert ttl_route == 7200  # 2 * 3600
        
        # 测试默认TTL
        ttl_default = self.cache_manager._get_ttl("unknown_tool")
        assert ttl_default == 3600  # 1 * 3600
    
    @pytest.mark.asyncio
    async def test_cache_miss(self):
        """测试缓存未命中"""
        with patch.object(self.cache_manager, '_get_redis_client') as mock_redis:
            mock_client = AsyncMock()
            mock_client.get.return_value = None
            mock_redis.return_value = mock_client
            
            result = await self.cache_manager.get("amap_maps_geo", {"address": "北京"})
            
            assert result is None
            assert self.cache_manager._stats.total_requests == 1
            assert self.cache_manager._stats.cache_misses == 1
            assert self.cache_manager._stats.cache_hits == 0
    
    @pytest.mark.asyncio
    async def test_cache_hit(self):
        """测试缓存命中"""
        cached_data = {"status": "1", "geocodes": [{"location": "116.397477,39.908692"}]}
        
        with patch.object(self.cache_manager, '_get_redis_client') as mock_redis:
            mock_client = AsyncMock()
            mock_client.get.return_value = json.dumps(cached_data)
            mock_redis.return_value = mock_client
            
            result = await self.cache_manager.get("amap_maps_geo", {"address": "北京"})
            
            assert result == cached_data
            assert self.cache_manager._stats.total_requests == 1
            assert self.cache_manager._stats.cache_hits == 1
            assert self.cache_manager._stats.cache_misses == 0
    
    @pytest.mark.asyncio
    async def test_cache_write(self):
        """测试缓存写入"""
        data = {"status": "1", "geocodes": [{"location": "116.397477,39.908692"}]}
        
        with patch.object(self.cache_manager, '_get_redis_client') as mock_redis:
            mock_client = AsyncMock()
            mock_client.setex.return_value = True
            mock_redis.return_value = mock_client
            
            success = await self.cache_manager.set("amap_maps_geo", {"address": "北京"}, data)
            
            assert success is True
            
            # 验证调用参数
            mock_client.setex.assert_called_once()
            call_args = mock_client.setex.call_args
            
            # 验证TTL为地理编码的默认值（10天）
            assert call_args[0][1] == 864000
            
            # 验证数据序列化
            assert json.loads(call_args[0][2]) == data
    
    @pytest.mark.asyncio
    async def test_cache_write_with_custom_ttl(self):
        """测试自定义TTL的缓存写入"""
        data = {"test": "data"}
        custom_ttl = 3600
        
        with patch.object(self.cache_manager, '_get_redis_client') as mock_redis:
            mock_client = AsyncMock()
            mock_client.setex.return_value = True
            mock_redis.return_value = mock_client
            
            success = await self.cache_manager.set(
                "test_tool", 
                {"param": "value"}, 
                data, 
                ttl_override=custom_ttl
            )
            
            assert success is True
            
            # 验证使用了自定义TTL
            call_args = mock_client.setex.call_args
            assert call_args[0][1] == custom_ttl
    
    @pytest.mark.asyncio
    async def test_cache_delete(self):
        """测试缓存删除"""
        with patch.object(self.cache_manager, '_get_redis_client') as mock_redis:
            mock_client = AsyncMock()
            mock_client.delete.return_value = 1  # 删除了1个键
            mock_redis.return_value = mock_client
            
            success = await self.cache_manager.delete("amap_maps_geo", {"address": "北京"})
            
            assert success is True
            mock_client.delete.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_cache_delete_not_found(self):
        """测试删除不存在的缓存"""
        with patch.object(self.cache_manager, '_get_redis_client') as mock_redis:
            mock_client = AsyncMock()
            mock_client.delete.return_value = 0  # 没有删除任何键
            mock_redis.return_value = mock_client
            
            success = await self.cache_manager.delete("amap_maps_geo", {"address": "不存在"})
            
            assert success is False
    
    @pytest.mark.asyncio
    async def test_cache_clear_pattern(self):
        """测试模式匹配删除"""
        with patch.object(self.cache_manager, '_get_redis_client') as mock_redis:
            mock_client = AsyncMock()
            
            # 模拟SCAN结果
            mock_keys = [
                "cache:tool:amap_maps_geo:hash1",
                "cache:tool:amap_maps_weather:hash2",
                "cache:tool:amap_maps_search:hash3"
            ]
            
            async def mock_scan_iter(match):
                for key in mock_keys:
                    if "amap_" in key:
                        yield key
            
            mock_client.scan_iter = mock_scan_iter
            mock_client.delete = AsyncMock()
            mock_redis.return_value = mock_client
            
            deleted_count = await self.cache_manager.clear_pattern("cache:tool:amap_*")
            
            assert deleted_count == 3
            assert mock_client.delete.call_count == 3
    
    @pytest.mark.asyncio
    async def test_error_handling_redis_failure(self):
        """测试Redis连接失败的错误处理"""
        with patch.object(self.cache_manager, '_get_redis_client') as mock_redis:
            mock_redis.side_effect = Exception("Redis连接失败")
            
            # 测试读取失败
            result = await self.cache_manager.get("test_tool", {"param": "value"})
            assert result is None
            assert self.cache_manager._stats.cache_misses == 1
            
            # 测试写入失败
            success = await self.cache_manager.set("test_tool", {"param": "value"}, {"data": "test"})
            assert success is False
    
    @pytest.mark.asyncio
    async def test_corrupted_cache_data(self):
        """测试损坏的缓存数据处理"""
        with patch.object(self.cache_manager, '_get_redis_client') as mock_redis:
            mock_client = AsyncMock()
            mock_client.get.return_value = "invalid_json"  # 损坏的JSON
            mock_client.delete = AsyncMock()
            mock_redis.return_value = mock_client
            
            result = await self.cache_manager.get("test_tool", {"param": "value"})
            
            assert result is None
            # 验证删除了损坏的缓存
            mock_client.delete.assert_called_once()
    
    def test_stats_functionality(self):
        """测试统计功能"""
        # 初始状态
        stats = self.cache_manager.get_stats()
        assert stats.total_requests == 0
        assert stats.cache_hits == 0
        assert stats.cache_misses == 0
        assert stats.hit_rate == 0.0
        
        # 模拟一些请求
        self.cache_manager._stats.total_requests = 10
        self.cache_manager._stats.cache_hits = 7
        self.cache_manager._stats.cache_misses = 3
        
        stats = self.cache_manager.get_stats()
        assert stats.total_requests == 10
        assert stats.cache_hits == 7
        assert stats.cache_misses == 3
        assert stats.hit_rate == 0.7
        
        # 重置统计
        self.cache_manager.reset_stats()
        stats = self.cache_manager.get_stats()
        assert stats.total_requests == 0
        assert stats.hit_rate == 0.0
    
    @pytest.mark.asyncio
    async def test_cache_manager_close(self):
        """测试缓存管理器关闭"""
        # 模拟已建立连接的状态
        mock_client = AsyncMock()
        self.cache_manager._redis_client = mock_client
        
        # 关闭连接
        await self.cache_manager.close()
        
        mock_client.close.assert_called_once()
        assert self.cache_manager._redis_client is None


class TestCachedToolCallDecorator:
    """缓存装饰器测试类"""
    
    @pytest.mark.asyncio
    async def test_decorator_cache_miss(self):
        """测试装饰器缓存未命中"""
        
        @cached_tool_call('test_tool')
        async def test_function(param1: str, param2: int) -> dict:
            return {"result": f"{param1}_{param2}"}
        
        with patch('src.core.cache_manager.CacheManager') as mock_cache_class:
            mock_cache = AsyncMock()
            mock_cache.get.return_value = None  # 缓存未命中
            mock_cache.set.return_value = True
            mock_cache.close.return_value = None
            mock_cache_class.return_value = mock_cache
            
            result = await test_function("test", 123)
            
            assert result == {"result": "test_123"}
            
            # 验证缓存调用
            mock_cache.get.assert_called_once_with('test_tool', {'param1': 'test', 'param2': 123})
            mock_cache.set.assert_called_once_with('test_tool', {'param1': 'test', 'param2': 123}, {"result": "test_123"}, None)
    
    @pytest.mark.asyncio
    async def test_decorator_cache_hit(self):
        """测试装饰器缓存命中"""
        cached_result = {"cached": "data"}
        
        @cached_tool_call('test_tool')
        async def test_function(param: str) -> dict:
            # 这个函数不应该被调用
            raise AssertionError("Function should not be called when cache hits")
        
        with patch('src.core.cache_manager.CacheManager') as mock_cache_class:
            mock_cache = AsyncMock()
            mock_cache.get.return_value = cached_result  # 缓存命中
            mock_cache.close.return_value = None
            mock_cache_class.return_value = mock_cache
            
            result = await test_function("test")
            
            assert result == cached_result
            
            # 验证没有调用set（因为缓存命中）
            mock_cache.set.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_decorator_error_handling(self):
        """测试装饰器错误处理"""
        
        @cached_tool_call('test_tool')
        async def test_function(param: str) -> dict:
            return {"result": param}
        
        with patch('src.core.cache_manager.CacheManager') as mock_cache_class:
            mock_cache = AsyncMock()
            mock_cache.get.side_effect = Exception("缓存错误")
            mock_cache.close.return_value = None
            mock_cache_class.return_value = mock_cache
            
            # 即使缓存出错，函数也应该正常执行
            result = await test_function("test")
            
            assert result == {"result": "test"}


class TestGlobalCacheManager:
    """全局缓存管理器测试类"""
    
    @pytest.mark.asyncio
    async def test_global_cache_manager_singleton(self):
        """测试全局缓存管理器单例模式"""
        # 重置全局变量
        import src.core.cache_manager
        src.core.cache_manager._global_cache_manager = None
        
        manager1 = await get_cache_manager()
        manager2 = await get_cache_manager()
        
        # 验证返回相同实例
        assert manager1 is manager2 