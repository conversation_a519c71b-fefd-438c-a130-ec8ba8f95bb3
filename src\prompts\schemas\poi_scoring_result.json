{"type": "object", "properties": {"scored_pois": {"type": "array", "items": {"type": "object", "properties": {"poi_id": {"type": "string", "description": "POI的唯一标识符"}, "name": {"type": "string", "description": "POI名称"}, "category": {"type": "string", "enum": ["景点", "美食", "酒店", "停车场", "充电桩", "其他"], "description": "POI类别"}, "comprehensive_score": {"type": "number", "minimum": 0, "maximum": 10, "description": "综合评分，0-10分"}, "score_breakdown": {"type": "object", "properties": {"basic_quality": {"type": "number", "minimum": 0, "maximum": 10, "description": "基础质量评分"}, "preference_match": {"type": "number", "minimum": 0, "maximum": 10, "description": "用户偏好匹配评分"}, "memory_match": {"type": "number", "minimum": 0, "maximum": 10, "description": "历史记忆匹配评分"}, "practicality": {"type": "number", "minimum": 0, "maximum": 10, "description": "实用性评分"}}, "required": ["basic_quality", "preference_match", "memory_match", "practicality"], "additionalProperties": false}, "recommendation_reason": {"type": "string", "description": "推荐理由，说明为什么这个POI适合用户"}, "matched_preferences": {"type": "array", "items": {"type": "string"}, "description": "匹配的用户偏好列表"}, "special_features": {"type": "array", "items": {"type": "string"}, "description": "特色亮点列表"}, "visit_priority": {"type": "string", "enum": ["高", "中", "低"], "description": "访问优先级"}}, "required": ["poi_id", "name", "category", "comprehensive_score", "score_breakdown", "recommendation_reason"], "additionalProperties": false}, "description": "评分后的POI列表，按综合得分从高到低排序"}, "scoring_summary": {"type": "object", "properties": {"total_pois": {"type": "integer", "description": "总POI数量"}, "high_score_count": {"type": "integer", "description": "高分POI数量（>7分）"}, "category_distribution": {"type": "object", "additionalProperties": {"type": "integer"}, "description": "各类别POI的数量分布"}, "avg_score": {"type": "number", "description": "平均评分"}, "scoring_criteria_weights": {"type": "object", "properties": {"basic_quality": {"type": "number", "description": "基础质量权重"}, "preference_match": {"type": "number", "description": "偏好匹配权重"}, "memory_match": {"type": "number", "description": "记忆匹配权重"}, "practicality": {"type": "number", "description": "实用性权重"}}, "required": ["basic_quality", "preference_match", "memory_match", "practicality"], "additionalProperties": false}}, "required": ["total_pois", "high_score_count", "category_distribution", "avg_score", "scoring_criteria_weights"], "additionalProperties": false}}, "required": ["scored_pois", "scoring_summary"], "additionalProperties": false}