# 智能旅行规划Agent - 产品需求文档 (PRD)

## 1. 文档概述 (Overview)

### 1.1 文档目的 (Purpose)
本文档旨在明确定义 **智能旅行规划Agent (TravelPlannerAgent)** 的产品需求、功能规格、技术架构和验收标准。它将作为研发、测试、产品团队之间沟通协作的基准，确保各方对产品有统一、清晰的理解。

### 1.2 项目背景 (Background)
当前用户进行旅行规划时，往往需要在多个应用（地图、点评、预订网站）之间切换，过程繁琐且信息碎片化。本项目旨在打造一个**对话式AI旅行规划入口**，通过一个统一的智能Agent，理解用户的口语化、模糊化需求，自动完成信息检索、路线规划、智能决策等一系列复杂任务，提供一站式的、个性化的旅行规划服务。

**设计依据**: 本文档的所有功能需求、API设计和数据模型，均以产品团队提供的UI/UX设计图为最终视觉和体验基准。

### 1.3 核心目标 (Core Objectives)
1.  **提升规划效率**: 将传统数小时的规划工作缩短至分钟级。
2.  **增强个性化体验**: 结合用户历史与偏好，提供"比用户更懂自己"的旅行建议。
3.  **保证信息时效性**: 实时调用外部API，确保路线、天气、POI信息的准确性。
4.  **优化交互体验**: 通过实时流式反馈，让用户清晰感知Agent的"思考过程"，降低等待焦虑。
5.  **提升维护效率**: 通过Prompt与代码解耦，实现快速迭代和热更新能力。

---

## 2. 功能需求 (Functional Requirements)

### **FR-NEW: Prompt管理与模板系统**
-   **描述**: Agent必须实现Prompt与代码的完全解耦，建立独立的模板管理系统，支持热更新和版本控制。
-   **核心需求**:
    1.  **模板文件系统**: 在`src/prompts/`目录下建立分类管理的Prompt模板，支持Markdown格式和Jinja2语法。
    2.  **动态渲染引擎**: 基于用户数据和上下文信息，动态渲染Prompt模板。
    3.  **Schema集成**: 自动将JSON Schema集成到Prompt中，确保LLM输出格式的一致性。
    4.  **缓存机制**: 模板解析结果缓存，提升系统性能。
    5.  **热更新支持**: 无需重启服务即可更新Prompt模板。
-   **技术实现**: 
    -   使用Jinja2作为模板引擎，支持变量渲染、条件判断、循环处理
    -   模板文件采用Markdown格式，便于产品经理直接编辑
    -   与现有JSON Schema系统无缝集成
-   **验收标准**: 产品经理能够直接修改`src/prompts/travel_planner/intent_extraction.md`文件，无需重启服务即可看到效果。

### FR-01: 自然语言旅行意图理解
-   **描述**: Agent必须能精准解析用户的自然语言输入，提取所有与旅行规划相关的核心实体和隐含意图。Agent在处理任何时间相关的查询前，必须首先获取当前系统时间作为基准。
-   **Prompt集成**: 使用`travel_planner/intent_extraction.md`模板，集成用户画像和时间基准数据。
-   **输入示例**:
    -   **直接查询**: `"周末带孩子从北京亦庄开车去故宫，找个好停车的地方和适合小孩吃的餐厅"`
    -   **标签式查询**: 用户在主页点击`"亲子出行"`、`"逛吃逛喝"`等标签，并输入目的地`"上海"`和天数`"3天"`。
    -   **混合查询**: ` "下周去上海出差，帮我推荐一个离静安寺近、带健身房的五星级酒店"`
    -   `"明天天气怎么样？适合去户外爬山吗？"`
-   **必须提取的实体**: 出发点、目的地、时间、出行方式、成员构成（亲子/情侣/单独）、预算、兴趣标签（网红/小众/美食）、特定需求（停车/充电/无障碍）。
-   **时间解析要求**: 
    -   **相对时间**: "周末"、"明天"、"下周"、"五一假期"等概念必须基于获取到的当前时间准确解析为具体日期。
    -   **绝对时间**: "2025年5月1日"等直接时间无需转换。
    -   **时长表达**: "3天2夜"、"一周"等时长概念需要结合起始时间计算出完整的日期范围。
-   **技术实现**: 使用`reasoning_llm`（如 `glm-z1-flash`）结合专用Prompt模板进行复杂的意图分析和实体抽取。

### FR-02: 个性化推荐与记忆
-   **描述**: Agent的推荐必须结合用户的历史行为、画像标签和过往行程记录。
-   **Prompt集成**: 在所有决策Prompt中自动集成用户画像数据，实现真正的个性化推荐。
-   **实现逻辑**:
    1.  **多源数据融合**: 根据`user_id`从**MySQL**的`dh_user_profile`数据库中加载完整用户档案：
        -   `user_memories`: 具体的用户偏好记忆（如"用户对海鲜过敏"、"用户喜欢摄影"）
        -   `user_summaries`: AI生成的用户画像摘要和关键词
        -   `user_travel_profiles`: 专属旅行偏好（旅行风格、住宿偏好、交通偏好）
        -   `user_vehicle_bindings`: 车辆信息，影响充电桩和停车场推荐
    2.  **家庭关系识别**: 从用户记忆中自动识别家庭成员关系（如"用户李小乐(ID:3)是该用户的儿子"），为亲子游提供更精准的推荐。
    3.  **个性化工具调用**: 将用户画像作为关键上下文，影响后续的工具调用参数和决策逻辑。
    4.  **学习闭环**: 任务结束后，将本次行程和用户反馈更新到相应的记忆表中，持续优化个性化能力。

### FR-03: 动态工具规划与并行执行
-   **描述**: Agent必须能根据解析出的意图，智能地规划需要调用哪些高德地图工具，并以最高效、最全面的方式（并行）执行，为后续决策提供丰富的数据支持。
-   **实现逻辑**:
    1.  **工具规划**: 使用`reasoning_llm`，基于意图生成一个工具调用计划（Tool Call Plan），该计划应包含工具名称和参数。
    2.  **分层并行执行**: 采用四层并行调用策略，确保逻辑清晰和执行高效。
        -   **第一层 (基础信息层)**: 并发获取规划所必需的时间基准、起点/终点坐标和天气。
            - `get_current_time()` 获取当前系统时间作为时间解析基准
            - `maps_geo(起点)`、`maps_geo(目的地)`、`maps_weather(目的地城市)`
        -   **第二层 (核心POI信息层)**: 基于目的地坐标，并发获取五大核心业务组的POI信息，并对每个返回的关键POI调用`maps_search_detail`获取详情。
            -   **🅿️ 停车信息组**: 搜索"停车场"、"地下停车场"。
            -   **🔋 充电信息组**: 搜索"充电桩"。
            -   **🍽️ 美食餐饮组**: 搜索"美食"、"当地特色"及用户偏好标签。
            -   **🎯 景点娱乐组**: 搜索"景点"及用户偏好标签。
            -   **🏨 住宿信息组**: 搜索"酒店"、"民宿"。
        -   **第三层 (路线与辅助信息层)**: 在获取核心POI后，开始规划具体路线并获取媒体信息。
            -   **🛣️ 路线规划组**: 调用`maps_direction_driving`和`maps_direction_walking`，并解析过路费、路况等。
            -   **📸 图片媒体组**: 汇总所有`detail`信息中的高质量图片。
        -   **第四层 (深度信息挖掘层)**: 进行个性化和应急信息准备。
            -   **🎯 个性化深度挖掘组**: 根据用户画像搜索"母婴室"、"拍照打卡地"等。
            -   **🚨 应急便民信息组**: 搜索"医院"、"药店"、"公共厕所"。
    3.  **技术栈**: 使用`aiohttp`或`httpx`库实现真正的异步HTTP请求，配合`asyncio.gather`执行并行调用。

### FR-04: 结构化思考过程流式输出 (Structured Thinking Process via SSE)
-   **描述**: 为完美匹配UI设计中的"思考过程"展示，Agent的完整执行过程必须以结构化的事件流形式，通过Server-Sent Events (SSE)实时推送给客户端。
-   **事件类型 (Event Types)**:
    -   `thinking_start`: 思考开始事件，标志着Agent开始处理请求。
    -   `thinking_step`: 核心思考步骤事件。**此事件将多次发送**，每次对应UI上的一个思考类别。
        -   **Payload**: `{ "category": "出行对象" | "出行时间" | "景点推荐" | "美食推荐" | "住宿推荐" | "其他", "content": "正在为您分析适合亲子出行的行程..." }`
    -   `tool_call`: 准备调用某个工具的事件，包含工具名称和参数。
    -   `tool_result`: 工具执行完成的事件，包含工具名称和返回结果的摘要。
    -   `planning_step`: 规划步骤事件，当Agent在生成具体行程时（如"正在规划DAY 1路线..."）发送。
    -   `final_itinerary`: 最终行程规划完成的事件，payload为完整的行程JSON。
    -   `error`: 流程中发生错误的事件。
-   **技术实现**: Agent主方法改造为`async def`生成器，使用`yield`推送结构化的`StreamEvent`对象。FastAPI后端使用`StreamingResponse`实现SSE接口。

### FR-05: 丰富化结构输出 (Rich Structured Output)
-   **描述**: Agent最终必须能生成一个信息完备、高度结构化的JSON对象，以驱动前端渲染复杂的行程详情页、地图页和编辑页。
-   **Prompt集成**: 使用专门的格式化Prompt模板，确保输出格式的一致性和完整性。
-   **核心产出**:
    1.  **结构化JSON行程单**: 这是唯一必须的输出。其数据结构必须包含渲染UI所需的所有信息。详见`trips`集合的数据库设计。
    2.  **可视化地图产品**: 在JSON行程单中，必须包含生成高德"沿途看"或`personal_map`所需的数据或直接生成的链接。同时，为关键路段（如酒店到景点）生成`maps_schema_navi`导航链接。
-   **技术实现**: 使用`basic_llm`（如`glm-4-flash`）执行格式化任务。接收`reasoning_llm`生成的内部逻辑计划，然后根据预设的Prompt模板，将其转换为最终的、严格遵循前端协议的JSON对象。

### FR-06: 完整执行日志持久化
-   **描述**: 每次规划任务的所有流式事件，必须在任务结束后完整地、按顺序地存入数据库，用于问题排查、数据分析和模型微调。
-   **实现逻辑**:
    1.  Agent在内存中维护一个`List[StreamEvent]`，每次`yield`事件时，也将其追加到此列表。
    2.  在`plan_trip`生成器的`finally`块中，调用数据库客户端，将完整的事件列表一次性写入MongoDB的`trip_execution_logs`集合。

### FR-07 (新增): 高级决策与智能编排
-   **描述**: Agent的核心决策过程，必须超越简单的信息罗列，实现真正的智能编排。
-   **Prompt集成**: 使用专门的决策Prompt模板组（`weather_analysis.md`、`poi_scoring.md`、`accommodation_decision.md`）进行智能推理。
-   **实现逻辑**:
    1.  **POI综合评分模型**:
        -   为停车场、餐厅、景点等不同类型的POI建立独立的加权评分模型。
        -   示例（停车场）：`Score = w1 * (1/价格) + w2 * (1/步行距离) + w3 * 评分`。
        -   根据综合评分筛选出Top N推荐。
    2.  **行程动态编排**:
        -   **核心景点精选**: 在行程规划前，LLM必须能从众多候选景点中，根据总天数和各景点预估游玩时间，智能判断并筛选出每日合理数量的核心景点（如1-2个），避免行程过满。
        -   **单日游览顺序优化**: 在确定一天内的所有活动后（如2个景点，1个餐厅），LLM需要根据它们的地理位置、开放时间和路况，智能规划出最优的访问顺序，减少奔波。
        -   **游玩时长智能估算 (Fallback)**: 当API未返回POI的"建议游玩时长"时，LLM需能基于POI类别（如博物馆、公园、商场）进行常识性估算，以支持行程规划的顺利进行。
        -   **智能匹配**: 基于POI评分和地理位置，为每日核心景点自动匹配评分最高的停车场和餐厅。
        -   **动态时间分配**: 根据景点的`建议游玩时长`和路线`耗时`，合理规划每日时间线，并自动加入冗余时间。
        -   **多日行程优化**: 对于多日游，优化住宿地点，使其位于行程的中心区域，减少重复路程。
        -   **方案与预案生成**:
            -   **创建备选方案(Plan B)**: 除了主推荐，额外提供一个基于不同偏好（如"悠闲漫步" vs "网红打卡"）的备选方案。
            -   **生成"旅行小贴士"**: 自动从各类信息中提炼并生成实用贴士，如"XX景点建议提前预订"、"XX餐厅下午5点后可能排队"等。

### FR-08 (新增): 行程后期编辑 (Post-Generation Itinerary Editing)
-   **描述**: 用户在收到AI生成的行程后，有权对其进行修改。系统必须提供相应的API支持前端实现这些编辑功能。
-   **核心编辑功能**:
    1.  **添加POI**: 用户可以在某一天的行程中，通过搜索或从推荐列表中添加新的POI。
    2.  **删除POI**: 用户可以从某一天的行程中删除一个已存在的POI。
    3.  **重新排序POI**: 用户可以拖拽调整某一天的POI访问顺序。
-   **技术实现**: 需要开发一组RESTful API来操作`trips`集合中的行程数据。详见API接口规范。

### FR-09 (新增): 精选路线管理 (Curated Itinerary Management)
-   **描述**: 支持在首页展示由运营人员预先配置的"精选路线"。
-   **实现逻辑**:
    1.  创建一个独立的`curated_trips`数据库集合，用于存储这些高质量的行程模板。
    2.  提供一个后台管理界面（或通过直接操作数据库），允许运营人员创建、编辑和发布精选路线。
    3.  开发一个API接口，供前端获取精选路线列表进行展示。

### FR-10 (新增): 下游功能支持 (Downstream Feature Support)
-   **描述**: Agent生成的行程JSON，其结构和信息丰富度必须足以支撑未来的下游AI功能。
-   **示例**:
    -   **AI Vlog生成**: 行程JSON中必须包含高质量的POI图片URL、地理坐标、趣味描述，作为AI视频生成器的基础素材。
    -   **行前准备清单**: 基于目的地天气、活动类型（爬山/游泳），自动生成物品准备清单。

### FR-11 (新增): 交互式景点确认 (可选实现)
-   **描述**: 为增强用户对规划的控制感，系统可提供一个交互式的景点确认环节。
-   **实现逻辑**:
    1.  在AI完成初步的景点精选与推荐后，不直接进入下一步，而是向用户展示推荐的1-2个核心景点及推荐理由。
    2.  界面上同时提供其他评分较高的候选景点列表。
    3.  用户可以接受AI的推荐，也可以从候选列表中替换或增加自己感兴趣的景点。
    4.  Agent将基于用户最终确认的景点列表，继续后续的住宿、餐饮规划。
-   **实现说明**: **此功能为可插拔模块，旨在增强用户控制感。初期版本可绕过此步骤，由AI直接完成景点选择，以加速流程。**

---

## 3. 系统设计与技术架构

### 3.1 架构模式: "形单神多"的单Agent架构
-   **实现上是单体**: 所有业务逻辑封装在`TravelPlannerAgent`一个类中，通过内部方法调用进行协作。便于初期快速开发和调试。
-   **思想上是多体**: `TravelPlannerAgent`内部通过不同的方法，扮演了规划器、意图分析器、工具执行器、决策器等多个角色，实现了高度的职责分离。为未来平滑演进到真正的多Agent系统（如AutoGen）奠定了基础。

### **3.2 新增：Prompt管理技术架构**

#### 3.2.1 设计原则
**核心思想**: 实现业务逻辑与文本生成的完全解耦，建立企业级的Prompt管理体系。

**设计原则**:
- **单一职责**: 每个Prompt模板只负责一个特定的决策点或任务
- **依赖注入**: 通过模板变量注入用户数据和上下文信息
- **版本控制**: 所有模板文件纳入Git版本管理
- **测试驱动**: 每个模板都支持独立的单元测试

#### 3.2.2 技术实现架构

**核心组件**:
```python
# 模板管理器架构
class TemplateManager:
    """Prompt模板管理器核心类"""
    
    def __init__(self):
        self.jinja_env = Environment(loader=FileSystemLoader("src/prompts"))
        self.schema_manager = SchemaManager()
        self.cache = TTLCache(maxsize=100, ttl=300)
    
    def render_template(self, template_path: str, **context) -> str:
        """渲染模板，支持缓存和变量注入"""
        pass
    
    def format_with_schema(self, prompt: str, schema_name: str) -> str:
        """自动集成JSON Schema到Prompt中"""
        pass
    
    def get_template_metadata(self, template_path: str) -> dict:
        """获取模板元数据（版本、作者、用途等）"""
        pass
```

**目录结构设计**:
```
src/prompts/
├── __init__.py                    # 导出TemplateManager
├── template_manager.py            # 核心模板引擎
├── schemas/                       # JSON Schema集合
│   ├── extract_travel_intent.json
│   ├── poi_scoring_result.json
│   ├── accommodation_decision_result.json
│   └── final_itinerary_format.json
├── travel_planner/                # 旅行规划专用模板
│   ├── __init__.py
│   ├── intent_extraction.md       # 意图理解
│   ├── accommodation_decision.md  # 住宿决策
│   ├── poi_scoring.md             # POI评分
│   ├── weather_analysis.md        # 天气分析
│   └── final_formatting.md        # 最终格式化
├── common/                        # 通用模板
│   ├── __init__.py
│   ├── error_handling.md          # 错误处理
│   └── system_prompts.md          # 系统级提示
└── templates/                     # 模板片段库
    ├── user_profile_section.md    # 用户画像片段
    ├── time_analysis_section.md   # 时间分析片段
    └── budget_constraint_section.md # 预算约束片段
```

**集成示例**:
```python
# Agent中的使用方式
class TravelPlannerAgent:
    def __init__(self):
        self.template_manager = TemplateManager()
        self.reasoning_llm = LLMManager().get_reasoning_llm()
    
    async def extract_intent(self, user_query: str, user_profile: dict) -> dict:
        # 从模板加载意图提取Prompt
        intent_prompt = self.template_manager.render_template(
            'travel_planner/intent_extraction.md',
            current_time=datetime.now().isoformat(),
            user_query=user_query,
            user_profile=user_profile
        )
        
        # 自动集成JSON Schema
        prompt_with_schema = self.template_manager.format_with_schema(
            intent_prompt,
            'extract_travel_intent.json'
        )
        
        # 调用LLM
        result = await self.reasoning_llm.generate(prompt_with_schema)
        return json.loads(result)
```

### 3.3 (新增) 核心架构: 工作流即图 (Workflow as a Graph)
**背景**: 受到`deer-flow-main`等业界领先项目的启发，为增强系统的健壮性、可维护性和可观测性，`TravelPlannerAgent`的核心逻辑应采用"工作流即图"的模式进行构建，推荐使用**LangGraph**框架。

**核心思想**:
Agent的执行流程不再是一系列线性的方法调用，而是一个**有向状态图 (Directed Acyclic Graph - DAG)**。
1.  **节点 (Nodes)**: 图中的每个节点代表一个原子操作或一个"专家角色"。例如：
    -   `intent_parser_node`: 解析用户意图。
    -   `tool_planning_node`: 规划需要调用的工具。
    -   `parallel_tool_executor_node`: 并行执行工具调用。
    -   `decision_synthesis_node`: 综合信息进行决策。
    -   `response_formatter_node`: 格式化最终输出。
2.  **边 (Edges)**: 图中的边代表了节点之间的流转关系，可以附带条件。例如，`tool_planning_node`完成后，可以根据"是否需要调用工具"这一条件，决定下一跳是`parallel_tool_executor_node`还是直接到`response_formatter_node`。
3.  **状态 (State)**: 一个统一的状态对象在图中传递。它包含所有需要的数据，如原始查询、解析后的意图、工具调用结果等。每个节点接收当前状态，执行操作，然后更新状态并返回。

**架构图示例**:
```mermaid
graph TD
    A[Start] --> B(intent_parser_node);
    B --> C(tool_planning_node);
    C --> D{has_tools_to_call?};
    D -- Yes --> E(parallel_tool_executor_node);
    E --> F(poi_scoring_node);
    F --> G(itinerary_orchestration_node);
    G --> H(response_formatter_node);
    D -- No --> H;
    H --> I[End];

    style B fill:#e1f5fe
    style C fill:#e8f5e8
    style E fill:#e8f5e8
    style F fill:#fff3e0
    style G fill:#fff3e0
    style H fill:#fce4ec
```

**优点**:
-   **可观测性 (Observability)**: 整个执行流程一目了然，便于追踪和调试。
-   **模块化 (Modularity)**: 节点之间高度解耦，可以独立开发、测试和替换。
-   **灵活性 (Flexibility)**: 可以通过修改图的结构，轻松地增删或重排步骤，而无需大规模重构代码。
-   **健壮性 (Robustness)**: 框架（如LangGraph）内置了重试、回退等机制，能方便地处理循环和错误。

### 3.4 数据流: 双通道策略
![双通道数据流](https://i.imgur.com/your-diagram-image.png) (*此处应有新绘制的、结合了SSE和DB的数据流图*)
```mermaid
sequenceDiagram
    participant Client
    participant FastAPI as "API Endpoint"
    participant Agent as "TravelPlannerAgent"
    participant TemplateManager as "Prompt Templates"
    participant DB as "MongoDB"

    Client->>+FastAPI: POST /api/v1/planner/stream
    FastAPI->>+Agent: plan_trip(query)

    Agent->>+TemplateManager: render_template(intent_extraction.md)
    TemplateManager-->>-Agent: rendered_prompt

    loop 实时流式通道 (SSE)
        Agent->>Agent: 生成结构化事件 (thinking_step, tool_call, ...)
        Note right of Agent: 缓存事件到内部列表
        Agent-->>FastAPI: yield StreamEvent
        FastAPI-->>Client: data: {event...}\n\n
    end

    Agent->>+TemplateManager: render_template(final_formatting.md)
    TemplateManager-->>-Agent: formatting_prompt

    Agent->>Agent: 任务完成
    Note right of Agent: 最终行程JSON已生成

    Agent-->>FastAPI: yield FinalItineraryEvent
    FastAPI-->>Client: data: {event...}\n\n

    Note over Agent, DB: 异步持久化通道
    Agent->>+DB: save_full_log([所有事件])
    DB-->>-Agent: 确认写入

    Agent-->>-FastAPI: 结束生成器
    FastAPI->>Client: 关闭SSE连接

    %% Post-generation editing flow
    Client->>+FastAPI: PUT /api/v1/itinerary/{id}/day/1/reorder
    FastAPI->>+DB: updateTrip(id, new_order)
    DB-->>-FastAPI: success
    FastAPI-->>-Client: 200 OK
```

### 3.5 模型策略: 分层模型调用
-   **决策层 (Thinking Layer)**: 使用`reasoning_llm` (`glm-z1-flash`)。负责意图理解、工具规划、**LLM驱动的智能决策**等所有复杂的思考任务。重点是**基于模型推理而非规则**进行复杂的情境判断和个性化权衡。输出一个逻辑完备的"内部计划"。
-   **执行层 (Formatting Layer)**: 使用`basic_llm` (`glm-4-flash`)。负责将"内部计划"转换为用户友好的、严格格式化的最终JSON。成本低、速度快。
-   **Prompt层 (Template Layer)**: 通过独立的模板管理系统，为每个模型调用提供动态渲染的Prompt，确保一致性和可维护性。

### 3.6 LLM智能决策技术架构 (重要技术突破)

**核心理念转变**: 从"基于规则的硬编码算法"彻底转向"LLM模型推理驱动的智能决策"。

#### 3.6.1 LLM决策输入上下文设计
```python
llm_reasoning_context = {
    # 用户档案数据 (来自MySQL)
    "user_profile": {
        "travel_style": "FAMILY",
        "budget_preference": "moderate",  
        "family_members": [{"relation": "儿子", "age": 4}],
        "travel_memories": ["喜欢拍照", "对海鲜过敏", "偏好安静住宿"]
    },
    
    # 当前规划任务
    "current_request": {
        "destination": "北京",
        "dates": ["2025-06-21", "2025-06-22"],
        "budget_constraint": 3000,
        "special_requirements": ["带4岁孩子", "自驾出行"]
    },
    
    # 外部环境数据
    "external_context": {
        "weather_forecast": [{"date": "2025-06-21", "weather": "雷阵雨"}],
        "current_time": "2025-01-27T14:30:00+08:00",
        "holiday_status": "工作日"
    },
    
    # 候选POI数据 (Phase 2并行搜索结果)
    "poi_candidates": {
        "attractions": [...],
        "restaurants": [...], 
        "hotels": [...],
        "parking": [...]
    }
}
```

#### 3.6.2 核心LLM推理任务（基于Prompt模板）

**1. POI智能筛选与匹配推理** (使用`poi_scoring.md`模板)
```markdown
# POI综合评分与智能推荐

## 用户画像
{{ user_profile | tojson(indent=2) }}

## 候选POI数据
{% for category, pois in poi_candidates.items() %}
### {{ category }}类POI
{% for poi in pois %}
- **{{ poi.name }}**: 评分{{ poi.rating }}, 距离{{ poi.distance }}米
{% endfor %}
{% endfor %}

## 推理任务
基于用户画像和候选POI，进行智能匹配和评分...
```

**2. 住宿需求判断与策略分析推理** (使用`accommodation_decision.md`模板)
```markdown
# 住宿需求智能分析与策略制定

## 基础信息
- 出发地：{{ origin }}
- 目的地：{{ destination }}
- 行程天数：{{ days }}天
- 出行方式：{{ transport_mode }}
- 用户预算：{{ budget }}元

## 景点地理分布
{{ attraction_distribution | tojson(indent=2) }}

## 分析任务
请按以下步骤进行住宿策略分析...
```

#### 3.6.3 Prompt模板的技术优势

与传统硬编码Prompt相比，基于模板管理的方案具有以下优势：

| 传统硬编码方法 | Prompt模板管理方法 |
|-----------------|-------------------|
| 所有文本硬编码在Python代码中 | 文本独立存储在Markdown文件中 |
| 修改需要重启服务 | 支持热更新，无需重启 |
| 版本管理困难 | 每个模板独立版本控制 |
| 产品经理无法直接修改 | 产品经理可直接编辑Markdown |
| 难以进行A/B测试 | 支持多版本模板并行测试 |
| 国际化困难 | 便于多语言版本管理 |

---

## 4. 外部工具集成: 高德地图 (Amap MCP)

### 4.1 工具列表与能力
Agent必须具备调用以下高德地图API的能力，这些API通过高德MCP SSE接口提供：
-   **定位与地址解析**: `maps_geo`, `maps_regeocode`, `maps_ip_location`
-   **路线规划**: `maps_direction_driving`, `maps_direction_walking`, `maps_direction_bicycling`, `maps_direction_transit_integrated`
-   **信息检索**: `maps_text_search`, `maps_around_search`, `maps_search_detail`
-   **辅助信息**: `maps_weather`, `maps_distance`
-   **App唤醒**: `maps_schema_personal_map`, `maps_schema_navi`, `maps_schema_take_taxi`

### 4.2 配置与认证
-   **接口地址**: `https://mcp.amap.com/sse`
-   **API Key**: `e8f742bdb09f99c8c6b035b7f1f04e66` (此密钥应通过环境变量`AMAP_MCP_API_KEY`加载，而非硬编码)

---

## 5. 数据库设计 (MongoDB)

### 5.1 数据库配置
配置信息从`config/default.yaml`中读取相应数据库部分：

**MySQL (主要用户数据)**:
-   **Host**: 配置中的mysql部分
-   **Databases**: `dh_user_profile`（用户档案）、`dh_tripplanner`（行程数据）

**MongoDB (日志与非结构化数据)**:
-   **Host**: `***********`
-   **Port**: `27017`
-   **Username/Password**: 通过环境变量加载
-   **Database**: `dh_platform_data`

### 5.2 数据模型架构

本系统采用**MySQL + MongoDB**双数据库架构：
- **MySQL**: 存储结构化的用户档案、行程数据、POI信息
- **MongoDB**: 存储执行日志、对话历史等非结构化数据

#### MySQL数据库表结构

**dh_user_profile库**：

1. **`users`** - 核心用户表
```sql
CREATE TABLE `users` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `nickname` VARCHAR(50) NOT NULL,
  `avatar_url` VARCHAR(255),
  `status` ENUM('ACTIVE', 'SUSPENDED', 'DELETED') DEFAULT 'ACTIVE',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

2. **`user_memories`** - AI长期记忆表
```sql
CREATE TABLE `user_memories` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT NOT NULL,
  `memory_content` TEXT NOT NULL,
  `confidence` FLOAT DEFAULT 1.0,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

3. **`user_travel_profiles`** - 专属旅行画像表
```sql
CREATE TABLE `user_travel_profiles` (
  `user_id` INT PRIMARY KEY,
  `travel_style` ENUM('RELAXED', 'ADVENTURE', 'FAMILY', 'BUDGET', 'LUXURY', 'BUSINESS'),
  `accommodation_pref` JSON,
  `transportation_pref` JSON,
  `travel_summary` TEXT,
  `travel_keywords` JSON
);
```

**dh_tripplanner库**：

4. **`ai_planning_sessions`** - AI规划任务会话表
```sql
CREATE TABLE `ai_planning_sessions` (
  `id` VARCHAR(36) PRIMARY KEY,
  `user_id` INT NOT NULL,
  `status` ENUM('PENDING', 'PROCESSING', 'SUCCESS', 'FAILED', 'CANCELED') DEFAULT 'PENDING',
  `user_input` JSON NOT NULL,
  `planning_log` LONGTEXT,
  `raw_llm_output` JSON,
  `final_itinerary_id` INT,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `completed_at` TIMESTAMP NULL
);
```

#### MongoDB集合结构

5. **`ai_interaction_logs` 集合**
存储完整的AI交互日志和执行过程。
```json
{
  "_id": "ObjectId('trip456')",
  "trace_id": "uuid-for-the-planning-session",
  "user_id": "user123",
  "created_at": "ISODate()",
  "updated_at": "ISODate()",
  "status": "completed" | "in_progress" | "failed",
  "raw_user_query": "周末带孩子去故宫...",
  "summary": {
    "title": "北京周末亲子文化游",
    "days": 2,
    "destination_city": "北京",
    "tags": ["亲子", "文化", "自驾"],
    "cover_image_url": "https://.../cover.jpg",
    "description": "由于周末有雨，已为您优先安排室内活动，行程兼顾了孩子的兴趣与知识的增长。"
  },
  "weather_forecast": [
    {
      "date": "2025-06-21",
      "day_weather": "雷阵雨", "night_weather": "多云",
      "day_temp": "30", "night_temp": "22"
    }
  ],
  "map_info": {
    "center_point": { "longitude": 116.4039, "latitude": 39.9151 },
    "route_polyline": "encoded_polyline_string_for_the_whole_trip",
    "personal_map_url": "https://... (高德行程地图链接)"
  },
  "daily_plans": [
    {
      "day": 1,
      "theme": "历史与艺术的探索",
      "pois": [
        {
          "poi_instance_id": "uuid-poi-1",
          "poi_id": "B000A82I63", // 高德POI ID
          "name": "故宫博物院",
          "category": "景点", // "景点" | "美食" | "酒店" | "购物" | "其他"
          "image_url": "https://.../gugong.jpg",
          "rating": 4.8,
          "address": "北京市东城区景山前街4号",
          "location": { "longitude": 116.4039, "latitude": 39.9151 },
          "opening_hours": "08:30-17:00",
          "estimated_duration_min": 240,
          "description": "中国的皇家宫殿，世界文化遗产...",
          "tips": "建议从午门进入，提前在线预订门票。",
          "navi_url": "https://... (高德导航链接)"
        },
        // ... more POIs for Day 1
      ]
    }
    // ... more daily_plans
  ],
  "budget_estimation": {
    "total_min": 800,
    "total_max": 1500,
    "breakdown": [
      { "category": "门票", "amount": 200 },
      { "category": "餐饮", "amount": 400 },
      { "category": "交通", "amount": 200 }
    ]
  }
}
```

#### 3. `trip_execution_logs` 集合
存储单次规划任务的完整执行日志，即所有SSE事件的集合。
```json
{
  "_id": "ObjectId('log789')",
  "trace_id": "uuid-for-the-planning-session",
  "user_id": "user123",
  "created_at": "ISODate()",
  "log": [
    {
      "event_id": "uuid-1",
      "event_type": "thought",
      "payload": { "message": "开始解析用户需求..." },
      "timestamp": "ISODate()"
    },
    {
      "event_id": "uuid-2",
      "event_type": "tool_call",
      "payload": { "name": "maps_weather", "params": {"city": "北京"} },
      "timestamp": "ISODate()"
    }
    // ... 更多事件
  ]
}
```

#### 4. `curated_trips` (新增)
存储由运营人员创建的"精选路线"。其结构与`trips`集合非常相似，但可能包含额外的管理字段。
```json
{
  "_id": "ObjectId('curated1')",
  "title": "新疆北疆环线-漫游",
  "author": "官方推荐",
  "is_published": true,
  "tags": ["自驾", "自然风光", "摄影"],
  "itinerary_data": {
    // ... 此处结构与 trips 集合的结构一致
  }
}
```

#### 5. `poi_preferences` (新增)
存储用户对特定POI的隐式或显式偏好。
```json
{
  "_id": "ObjectId('pref1')",
  "user_id": "user123",
  "poi_id": "B0FFG8J13Y", // 高德POI ID
  "preference_score": 0.8, // 0-1的偏好分数，通过用户选择、停留时间等计算
  "last_interacted_at": "ISODate()"
}
```

#### 6. `route_efficiency` (新增)
存储用户对某条规划路线的效率反馈，用于优化未来路线推荐。
```json
{
  "_id": "ObjectId('route1')",
  "route_hash": "md5(origin_loc+dest_loc+waypoints)", // 路线的唯一标识
  "user_id": "user123",
  "planned_duration_sec": 3600,
  "actual_duration_sec": 4000, // 可通过车机或用户反馈获取
  "feedback": "拥堵", // "畅通" | "拥堵" | "一般"
  "created_at": "ISODate()"
}
```

---

## 6. API接口规范

### 6.1 流式规划接口
-   **Endpoint**: `POST /api/v1/planner/stream`
-   **Request Body**:
    ```json
    {
      "user_id": "user123",
      "query": "周末带孩子去故宫玩，车停哪儿方便？",
      "session_id": "optional-session-uuid"
    }
    ```
-   **Response**:
    -   **Content-Type**: `text/event-stream`
    -   **Format**: 每个事件都是`data: <json_string>\n\n`的形式。

### 6.2 行程编辑接口 (新增)
-   **Endpoint**: `POST /api/v1/itinerary/{trip_id}/day/{day_num}/poi`
-   **Action**: 在指定行程的某一天添加一个新的POI。
-   **Request Body**: `{ "poi_id": "B000A82I63", "poi_data": { ... } }`
-   ---
-   **Endpoint**: `DELETE /api/v1/itinerary/{trip_id}/day/{day_num}/poi/{poi_instance_id}`
-   **Action**: 删除一个POI。
-   ---
-   **Endpoint**: `PUT /api/v1/itinerary/{trip_id}/day/{day_num}/reorder`
-   **Action**: 对某一天的POI列表重新排序。
-   **Request Body**: `{ "ordered_poi_instance_ids": ["uuid-poi-3", "uuid-poi-1", "uuid-poi-2"] }`

### 6.3 精选路线接口 (新增)
-   **Endpoint**: `GET /api/v1/curated-trips`
-   **Action**: 获取已发布的精选路线列表。
-   **Query Params**: `?tag=亲子&limit=10&page=1`

### 6.4 SSE事件模型 `StreamEvent`
```json
// The JSON string in the 'data' field of SSE
{
  "event_id": "uuid-of-this-event",
  "trace_id": "uuid-of-the-entire-trip-request",
  "event_type": "thinking_start" | "thinking_step" | "tool_call" | "tool_result" | "planning_step" | "final_itinerary" | "error",
  "payload": {
    // 结构取决于 event_type
  },
  "timestamp": "ISO 8601 string"
}
```

---

## 7. 配置管理

所有配置项应在`config/default.yaml`中定义，并支持通过环境变量覆盖。

### 7.1 `default.yaml` 配置引用
-   `reasoning_llm`: 用于决策层。
-   `basic_llm`: 用于执行层。
-   `mongodb`: 用于数据库连接。
-   `logging`: 用于配置应用日志。

### 7.2 新增配置项
建议在`default.yaml`中增加`amap`配置节：
```yaml
# Amap MCP Tool Settings
amap:
  base_url: "https://mcp.amap.com/sse"
  # api_key 通过环境变量 AMAP_API_KEY 加载
```

---

## 8. 开发里程碑 (Development Milestones)

-   **M1: Prompt管理系统与核心Agent基础 (Sprint 1)**
    -   [ ] **[核心新增]** 建立完整的Prompt管理系统：实现`TemplateManager`类、建立目录结构、集成Jinja2引擎。
    -   [ ] 实现`TravelPlannerAgent`基本类结构，集成Prompt模板管理器。
    -   [ ] **[新增]** 集成系统时间获取功能，支持相对时间解析。
    -   [ ] 迁移意图理解Prompt到模板文件（`intent_extraction.md`），实现动态渲染。
    -   [ ] 集成高德地图工具，实现**顺序**调用。
    -   [ ] **[新增]** 实现MySQL用户档案数据读取（`user_memories`, `user_travel_profiles`等）。
    -   [ ] **验收标准**: Agent能正确解析"周末去故宫"等时间表达，产品经理能直接修改`intent_extraction.md`文件并立即生效。

-   **M2: 决策Prompt模板迁移与流式输出 (Sprint 2)**
    -   [ ] **[Prompt迁移]** 迁移所有决策相关Prompt到模板文件：`poi_scoring.md`、`accommodation_decision.md`、`weather_analysis.md`。
    -   [ ] 将Agent主方法改造为异步生成器，集成模板渲染流程。
    -   [ ] 实现FastAPI的SSE流式接口。
    -   [ ] **[重要调整]** 实现分步规划架构：先景点→再住宿→后配套→最终整合。
    -   [ ] **[新增]** 实现"思考过程"交互式补全功能。
    -   [ ] 实现基于模板的LLM住宿策略决策模块。
    -   [ ] **验收标准**: UI能实时显示分步过程，产品经理能直接优化决策Prompt模板并立即生效。

-   **M3: 双数据库集成与持久化 (Sprint 3)**
    -   [ ] **[新增]** 实现MySQL连接与用户档案数据模型操作。
    -   [ ] **[新增]** 实现`ai_planning_sessions`会话表的读写逻辑。
    -   [ ] 实现MongoDB的连接与执行日志存储。
    -   [ ] 完成MySQL行程数据与MongoDB日志的双写逻辑。
    -   [ ] **验收标准**: 每次规划后，MySQL中有完整的行程数据，MongoDB中有详细的执行日志。

-   **M4: 完整Prompt体系与智能决策优化 (Sprint 4)**
    -   [ ] **[Prompt完善]** 完成所有剩余Prompt模板迁移：`final_formatting.md`、`error_handling.md`等。
    -   [ ] **[增强]** 实现基于MySQL用户记忆的深度个性化推荐，优化相关Prompt模板。
    -   [ ] **[新增]** 实现预算约束下的LLM智能决策（住宿、餐饮、交通）。
    -   [ ] **[住宿专项]** 完善住宿决策Prompt模板，支持同城/异地住宿策略智能判断。
    -   [ ] **[新增]** 实现家庭关系识别和亲子游专项推荐。
    -   [ ] 实现POI综合评分模型和动态行程编排逻辑。
    -   [ ] 建立Prompt模板版本管理和A/B测试机制。
    -   [ ] **验收标准**: Agent能识别用户的家庭成员，严格遵守预算约束，产品经理能通过编辑Prompt模板直接优化推荐效果。

---

## 9. (新增) 调用策略与分级 (Calling Strategy & Levels)
基于业务复杂度和用户需求，Agent采用不同的工具调用策略：

### 9.1 基础信息查询 (Level 1)
*单一信息点查询，1-2个工具调用*
- **示例**: "查一下北京今天的天气"
- **工具链**: `maps_weather`
- **响应**: 直接返回天气信息。

### 9.2 路线规划查询 (Level 2)
*涉及地理计算，3-5个工具调用*
- **示例**: "从三里屯开车到颐和园要多久？停车方便吗？"
- **工具链**: 并行`maps_geo` -> `maps_direction_driving` -> `maps_around_search(停车场)` -> `maps_search_detail`
- **响应**: 路线时间、费用、推荐停车场及收费标准。

### 9.3 周边探索查询 (Level 3)
*区域性需求，8-15个工具调用*
- **示例**: "我在三里屯，附近有什么好吃的川菜馆？要有停车位的"
- **工具链**: `maps_geo` -> 并行`maps_around_search(川菜)`+`maps_around_search(停车场)` -> 并行`maps_search_detail` -> `maps_distance`
- **响应**: 推荐3家川菜馆 + 最佳停车方案 + 步行路线。

### 9.4 全方位旅行规划 (Level 4)
*复合需求，20-40个工具调用，四层并行架构*
- **示例**: "周末带孩子从亦庄开车去故宫，想玩一整天，求推荐路线"
- **工具链**: 完整四阶段工作流（Phase 1: 意图解析 + 用户画像加载 -> Phase 2: 四层并行调用 -> Phase 3: 综合决策与智能匹配 -> Phase 4: 多格式结果生成）
- **响应**: 完整一日游攻略 + 高德地图链接 + 费用明细 + 实用贴士。

### 9.5 跨城市复杂规划 (Level 5)
*最高复杂度，50+工具调用*
- **示例**: "五一假期从北京自驾去天津2天1夜游，求详细攻略"
- **工具链**: 超级工作流（多城市并行信息获取 + 二日行程优化算法 + 预案与应急）
- **响应**: 详细2日攻略 + 每日导航链接 + 费用预算 + 应急预案。

---

## 10. 验收标准 (Acceptance Criteria)

### 10.1 Prompt管理系统验收 (Template Management Acceptance)
1.  **热更新验证**: 产品经理修改`src/prompts/travel_planner/intent_extraction.md`文件后，无需重启服务，下一次请求立即生效。
2.  **模板渲染验证**: 模板能正确渲染用户画像、时间基准等动态数据，变量替换准确率100%。
3.  **Schema集成验证**: JSON Schema能自动集成到Prompt中，LLM输出格式符合预期。
4.  **版本控制验证**: 每个模板文件都有完整的Git版本历史，支持回滚和对比。
5.  **缓存性能验证**: 相同模板的重复渲染使用缓存，响应时间<10ms。

### 10.2 功能性验收 (Functional Acceptance)
1.  **意图理解准确性**: Agent能正确解析各种复杂、口语化的查询，实体抽取准确率≥95%。
    - 示例："下周末和老婆带4岁儿子从上海浦东自驾去杭州西湖2天1夜，预算控制在5000以内，求推荐"
    - 期望：正确提取出发地(上海浦东)、目的地(杭州西湖)、时间(转换为具体日期)、成员(夫妻+4岁儿童)、预算约束(5000元)、出行方式(自驾)等关键信息。

2.  **个性化推荐有效性**: 基于用户画像的推荐必须体现个性化差异。
    - 示例：同样的"北京一日游"查询，对"美食爱好者"和"历史文化爱好者"应产生明显不同的推荐结果。

3.  **时间解析准确性**: 各种时间表达均能基于当前时间正确解析。
    - 示例：当前时间为"2025年1月27日周一14:30"时，"这个周末"应解析为"2025年2月1日-2日"。

4.  **结构化输出完整性**: 生成的JSON必须包含前端渲染所需的所有字段，且格式严格符合数据库schema。

5.  **流式事件完整性**: SSE事件流必须完整覆盖整个规划过程，事件类型和payload格式符合前端协议。

### 10.3 性能验收 (Performance Acceptance)
1.  **模板渲染性能**: 单个模板渲染时间≤10ms（缓存命中）或≤50ms（缓存未命中）。
2.  **响应时间**: Level 1查询≤3秒，Level 2-3查询≤15秒，Level 4-5查询≤45秒。
3.  **并发处理**: 支持至少10个并发用户同时进行旅行规划，无明显性能下降。
4.  **SSE稳定性**: 长时间SSE连接（45秒+）保持稳定，不出现连接中断。

### 10.4 质量验收 (Quality Acceptance)
1.  **数据一致性**: MySQL和MongoDB中的相关数据保持一致，无数据丢失。
2.  **错误恢复**: 外部API调用失败时，Agent能优雅降级并提供有用的部分结果。
3.  **用户体验**: 流式输出确保用户始终能感知到Agent的"思考进度"，无长时间静默期。
4.  **可维护性**: 产品经理能够独立优化和测试Prompt模板，无需技术团队介入。

---

## 11. 风险评估与缓解措施 (Risk Assessment & Mitigation)

### 11.1 技术风险
-   **Prompt模板热更新风险**: 模板语法错误可能导致渲染失败。
    -   **缓解措施**: 实现模板语法校验和回滚机制，生产环境使用经过测试的模板版本。
-   **LLM调用失败风险**: 外部LLM服务不稳定可能导致规划失败。
    -   **缓解措施**: 实现多模型备份策略和重试机制。
-   **高德地图API限制**: API调用频次或quota限制。
    -   **缓解措施**: 实现结果缓存、调用频次控制和备用数据源。

### 11.2 业务风险
-   **推荐质量下降**: Prompt模板优化不当可能导致推荐效果变差。
    -   **缓解措施**: 建立A/B测试机制，新模板版本先在小范围用户中测试。
-   **用户期望过高**: 用户对AI规划的期望可能超出技术能力。
    -   **缓解措施**: 在UI中明确说明AI的能力边界，提供编辑和个性化选项。

### 11.3 运维风险
-   **模板版本管理混乱**: 多人编辑模板可能导致版本冲突。
    -   **缓解措施**: 实现严格的Git工作流和审批机制，重要模板修改需要技术审核。

---

## 12. 后续扩展规划 (Future Extensions)

### 12.1 多语言支持
-   基于现有Prompt模板架构，可轻松扩展支持英文、日文等多语言版本。
-   每种语言对应独立的模板目录，共享相同的业务逻辑。

### 12.2 细分领域专家模板
-   针对特定场景（商务出行、亲子游、情侣游、背包客）开发专用Prompt模板。
-   通过模板组合实现更精准的个性化推荐。

### 12.3 实时学习优化
-   基于用户反馈和行为数据，自动优化Prompt模板中的权重和表述。
-   实现Prompt模板的自动生成和版本迭代。

---

**文档版本**: v2.1  
**最后更新**: 2025年1月27日  
**更新内容**: 新增Prompt管理系统需求和技术架构设计  
**负责人**: AI Team & Product Team