#!/usr/bin/env python3
"""
AutoPilot AI - 缓存系统快速验证脚本

独立运行的脚本，用于验证缓存系统的基本功能：
- Redis 连接测试
- 缓存读写测试  
- TTL 配置验证
- 性能基准测试
- 与旅行规划 Agent 的集成测试

使用方法：
    python tests/scripts/test_cache_system.py
"""

import asyncio
import json
import time
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.cache_manager import CacheManager, get_cache_manager
from src.core.config import get_settings
from src.agents.travel_planner_agent import TravelPlannerAgent
from src.core.logger import get_logger

logger = get_logger("cache_test")


class CacheSystemTester:
    """缓存系统测试器"""
    
    def __init__(self):
        self.cache_manager = None
        self.agent = None
        self.results = {
            "redis_connection": False,
            "basic_cache_ops": False,
            "ttl_verification": False,
            "performance_test": False,
            "agent_integration": False,
            "errors": []
        }
    
    async def setup(self):
        """初始化测试环境"""
        try:
            print("🚀 初始化缓存系统测试环境...")
            
            # 初始化缓存管理器
            self.cache_manager = await get_cache_manager()
            
            # 初始化旅行规划Agent
            self.agent = TravelPlannerAgent()
            
            print("✅ 测试环境初始化完成")
            
        except Exception as e:
            error_msg = f"测试环境初始化失败: {str(e)}"
            print(f"❌ {error_msg}")
            self.results["errors"].append(error_msg)
            raise
    
    async def test_redis_connection(self):
        """测试 Redis 连接"""
        print("\n📡 测试 Redis 连接...")
        
        try:
            # 测试基本连接
            redis_client = await self.cache_manager._get_redis_client()
            await redis_client.ping()
            
            print("✅ Redis 连接测试通过")
            self.results["redis_connection"] = True
            
        except Exception as e:
            error_msg = f"Redis 连接失败: {str(e)}"
            print(f"❌ {error_msg}")
            self.results["errors"].append(error_msg)
    
    async def test_basic_cache_operations(self):
        """测试基本缓存操作"""
        print("\n💾 测试基本缓存操作...")
        
        try:
            test_data = {
                "status": "1",
                "geocodes": [
                    {
                        "location": "116.397477,39.908692",
                        "formatted_address": "北京市东城区景山前街4号"
                    }
                ]
            }
            
            # 测试写入
            success = await self.cache_manager.set(
                "amap_maps_geo", 
                {"address": "北京故宫"}, 
                test_data
            )
            
            if not success:
                raise Exception("缓存写入失败")
            
            # 测试读取
            cached_result = await self.cache_manager.get(
                "amap_maps_geo", 
                {"address": "北京故宫"}
            )
            
            if cached_result != test_data:
                raise Exception("缓存读取结果不匹配")
            
            # 测试删除
            delete_success = await self.cache_manager.delete(
                "amap_maps_geo", 
                {"address": "北京故宫"}
            )
            
            if not delete_success:
                raise Exception("缓存删除失败")
            
            # 验证删除后读取为空
            after_delete = await self.cache_manager.get(
                "amap_maps_geo", 
                {"address": "北京故宫"}
            )
            
            if after_delete is not None:
                raise Exception("缓存删除后仍能读取到数据")
            
            print("✅ 基本缓存操作测试通过")
            self.results["basic_cache_ops"] = True
            
        except Exception as e:
            error_msg = f"基本缓存操作测试失败: {str(e)}"
            print(f"❌ {error_msg}")
            self.results["errors"].append(error_msg)
    
    def test_ttl_configuration(self):
        """测试 TTL 配置"""
        print("\n⏰ 测试 TTL 配置...")
        
        try:
            expected_ttls = {
                "amap_maps_geo": 864000,          # 10天
                "amap_maps_weather": 14400,       # 4小时
                "amap_maps_text_search": 86400,   # 24小时
                "amap_maps_direction_walking": 7200,  # 2小时
                "amap_traffic": 300,              # 5分钟
                "unknown_tool": 3600              # 默认1小时
            }
            
            for tool_name, expected_ttl in expected_ttls.items():
                actual_ttl = self.cache_manager._get_ttl(tool_name)
                
                if actual_ttl != expected_ttl:
                    raise Exception(f"{tool_name} TTL配置错误: 期望{expected_ttl}, 实际{actual_ttl}")
            
            print("✅ TTL 配置验证通过")
            self.results["ttl_verification"] = True
            
        except Exception as e:
            error_msg = f"TTL 配置测试失败: {str(e)}"
            print(f"❌ {error_msg}")
            self.results["errors"].append(error_msg)
    
    async def test_performance(self):
        """测试缓存性能"""
        print("\n🏃 测试缓存性能...")
        
        try:
            # 测试缓存键生成性能
            start_time = time.time()
            for i in range(1000):
                self.cache_manager._generate_cache_key("test_tool", {"param": f"value_{i}"})
            key_gen_time = time.time() - start_time
            
            print(f"   📊 缓存键生成性能: 1000次耗时 {key_gen_time:.3f}s")
            
            # 测试 TTL 查找性能
            start_time = time.time()
            for i in range(1000):
                self.cache_manager._get_ttl("amap_maps_geo")
            ttl_lookup_time = time.time() - start_time
            
            print(f"   📊 TTL查找性能: 1000次耗时 {ttl_lookup_time:.3f}s")
            
            # 性能基准检查
            if key_gen_time > 1.0:
                raise Exception(f"缓存键生成性能不达标: {key_gen_time:.3f}s > 1.0s")
            
            if ttl_lookup_time > 0.1:
                raise Exception(f"TTL查找性能不达标: {ttl_lookup_time:.3f}s > 0.1s")
            
            print("✅ 缓存性能测试通过")
            self.results["performance_test"] = True
            
        except Exception as e:
            error_msg = f"缓存性能测试失败: {str(e)}"
            print(f"❌ {error_msg}")
            self.results["errors"].append(error_msg)
    
    async def test_agent_integration(self):
        """测试与 Agent 的集成"""
        print("\n🤖 测试与旅行规划 Agent 的集成...")
        
        try:
            # 测试缓存调用方法是否存在
            if not hasattr(self.agent, '_cached_amap_call'):
                raise Exception("Agent 缺少 _cached_amap_call 方法")
            
            if not hasattr(self.agent, '_get_cache_manager'):
                raise Exception("Agent 缺少 _get_cache_manager 方法")
            
            # 测试缓存管理器获取
            agent_cache_manager = await self.agent._get_cache_manager()
            
            if agent_cache_manager is None:
                raise Exception("Agent 无法获取缓存管理器实例")
            
            print("✅ Agent 集成测试通过")
            self.results["agent_integration"] = True
            
        except Exception as e:
            error_msg = f"Agent 集成测试失败: {str(e)}"
            print(f"❌ {error_msg}")
            self.results["errors"].append(error_msg)
    
    async def test_cache_statistics(self):
        """测试缓存统计功能"""
        print("\n📈 测试缓存统计功能...")
        
        try:
            # 重置统计
            self.cache_manager.reset_stats()
            
            # 获取初始统计
            initial_stats = self.cache_manager.get_stats()
            
            if initial_stats.total_requests != 0:
                raise Exception("统计重置后 total_requests 应为 0")
            
            if initial_stats.hit_rate != 0.0:
                raise Exception("统计重置后 hit_rate 应为 0.0")
            
            print("✅ 缓存统计功能测试通过")
            
        except Exception as e:
            error_msg = f"缓存统计测试失败: {str(e)}"
            print(f"❌ {error_msg}")
            self.results["errors"].append(error_msg)
    
    async def cleanup(self):
        """清理测试环境"""
        try:
            if self.cache_manager:
                await self.cache_manager.close()
            print("🧹 测试环境清理完成")
        except Exception as e:
            print(f"⚠️ 清理过程中出现错误: {str(e)}")
    
    def print_summary(self):
        """打印测试结果摘要"""
        print("\n" + "="*60)
        print("📋 缓存系统测试结果摘要")
        print("="*60)
        
        total_tests = len([k for k in self.results.keys() if k != "errors"])
        passed_tests = len([k for k, v in self.results.items() if k != "errors" and v])
        
        print(f"总测试数: {total_tests}")
        print(f"通过数: {passed_tests}")
        print(f"失败数: {total_tests - passed_tests}")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        print("\n详细结果:")
        test_names = {
            "redis_connection": "Redis 连接",
            "basic_cache_ops": "基本缓存操作",
            "ttl_verification": "TTL 配置验证",
            "performance_test": "性能测试",
            "agent_integration": "Agent 集成"
        }
        
        for key, name in test_names.items():
            status = "✅ 通过" if self.results[key] else "❌ 失败"
            print(f"  {name}: {status}")
        
        if self.results["errors"]:
            print("\n❌ 错误详情:")
            for i, error in enumerate(self.results["errors"], 1):
                print(f"  {i}. {error}")
        
        print("\n" + "="*60)
        
        if passed_tests == total_tests:
            print("🎉 所有测试通过！缓存系统运行正常。")
            return True
        else:
            print("⚠️ 部分测试失败，请检查配置和环境。")
            return False


async def main():
    """主测试流程"""
    print("🔥 AutoPilot AI 缓存系统验证")
    print("="*60)
    
    tester = CacheSystemTester()
    
    try:
        # 初始化
        await tester.setup()
        
        # 执行所有测试
        await tester.test_redis_connection()
        await tester.test_basic_cache_operations()
        tester.test_ttl_configuration()
        await tester.test_performance()
        await tester.test_agent_integration()
        await tester.test_cache_statistics()
        
    except Exception as e:
        print(f"\n💥 测试过程中发生致命错误: {str(e)}")
        
    finally:
        await tester.cleanup()
        success = tester.print_summary()
        
        # 设置退出码
        sys.exit(0 if success else 1)


if __name__ == "__main__":
    # 运行测试
    asyncio.run(main()) 