#!/usr/bin/env python3
"""
测试STAB-01修复效果：验证state.orchestrated_itinerary字段是否正确设置

验证点：
1. _orchestrate_itinerary函数执行后state.orchestrated_itinerary不为None
2. state.orchestrated_itinerary包含期望的字段结构
3. _save_user_memory函数可以正常访问state.orchestrated_itinerary
"""

import asyncio
import sys
import os
import traceback
import uuid
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))
sys.path.insert(0, project_root)

from src.agents.travel_planner_agent import TravelPlannerAgent
from src.models.travel_planner import AgentState, TravelPlanRequest


async def test_orchestrated_itinerary_fix():
    """测试_orchestrate_itinerary修复效果"""
    print("=" * 60)
    print("🔧 STAB-01修复验证：state.orchestrated_itinerary字段设置")
    print("=" * 60)
    
    try:
        # 1. 创建Agent和状态对象
        print("📍 步骤1：创建TravelPlannerAgent和AgentState...")
        agent = TravelPlannerAgent()
        
        # 创建模拟的AgentState
        trace_id = str(uuid.uuid4())
        request = TravelPlanRequest(
            user_id="1001",  # 使用字符串类型
            query="我想去杭州玩2天",
            trace_id=trace_id
        )
        
        state = AgentState(
            trace_id=trace_id,
            user_id=request.user_id,
            original_query=request.query,  # 使用original_query字段
            extracted_entities={"destination": "杭州", "days": 2, "preferences": ["美食", "景点"]},
            tool_results={}
        )
        
        # 模拟scored_pois数据
        state.scored_pois = [
            {
                "id": "poi001",
                "name": "西湖风景名胜区",
                "category": "景点",
                "rating": 4.8,
                "address": "浙江省杭州市西湖区",
                "location": "120.153576,30.287459",
                "type": "风景区"
            },
            {
                "id": "poi002", 
                "name": "杭州博物馆",
                "category": "博物馆",
                "rating": 4.5,
                "address": "浙江省杭州市上城区",
                "location": "120.169585,30.246379",
                "type": "博物馆"
            },
            {
                "id": "poi003",
                "name": "灵隐寺",
                "category": "寺庙",
                "rating": 4.7,
                "address": "浙江省杭州市西湖区",
                "location": "120.101685,30.240379",
                "type": "佛教寺庙"
            }
        ]
        
        print("✅ AgentState创建成功，包含3个模拟POI")
        
        # 2. 验证修复前的状态
        print("\n📍 步骤2：验证修复前state.orchestrated_itinerary状态...")
        print(f"修复前 state.orchestrated_itinerary: {getattr(state, 'orchestrated_itinerary', '未定义')}")
        
        # 3. 调用_orchestrate_itinerary函数
        print("\n📍 步骤3：调用_orchestrate_itinerary函数...")
        event_count = 0
        async for event in agent._orchestrate_itinerary(state):
            event_count += 1
            print(f"  - 接收到事件 {event_count}: {event.event_type}")
            
        print(f"✅ _orchestrate_itinerary执行完成，产生{event_count}个事件")
        
        # 4. 验证修复后的状态
        print("\n📍 步骤4：验证修复后state.orchestrated_itinerary状态...")
        
        if hasattr(state, 'orchestrated_itinerary') and state.orchestrated_itinerary is not None:
            print("✅ state.orchestrated_itinerary已正确设置")
            
            # 验证数据结构
            expected_fields = ["destination", "days", "daily_plans", "total_pois", "orchestrated_at"]
            missing_fields = []
            
            for field in expected_fields:
                if field not in state.orchestrated_itinerary:
                    missing_fields.append(field)
                else:
                    print(f"  ✅ {field}: {type(state.orchestrated_itinerary[field])}")
            
            if missing_fields:
                print(f"  ❌ 缺失字段: {missing_fields}")
                return False
            else:
                print("✅ 所有期望字段都存在")
                
            # 验证daily_plans数据
            daily_plans = state.orchestrated_itinerary.get("daily_plans", [])
            print(f"  - daily_plans数量: {len(daily_plans)}")
            print(f"  - 总POI数量: {state.orchestrated_itinerary.get('total_pois', 0)}")
            print(f"  - 目的地: {state.orchestrated_itinerary.get('destination', '未知')}")
            print(f"  - 天数: {state.orchestrated_itinerary.get('days', 0)}")
            
        else:
            print("❌ state.orchestrated_itinerary仍然为None或未定义")
            return False
            
        # 5. 模拟_save_user_memory的关键调用
        print("\n📍 步骤5：模拟_save_user_memory中的关键调用...")
        try:
            import json
            raw_llm_output = json.dumps(state.orchestrated_itinerary, ensure_ascii=False)
            final_output = json.dumps(state.orchestrated_itinerary, ensure_ascii=False)
            
            print(f"✅ json.dumps(state.orchestrated_itinerary)调用成功")
            print(f"  - raw_llm_output长度: {len(raw_llm_output)}")
            print(f"  - final_output长度: {len(final_output)}")
            
        except Exception as e:
            print(f"❌ json.dumps调用失败: {e}")
            return False
            
        print("\n" + "=" * 60)
        print("🎉 STAB-01修复验证完全成功！")
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        print("详细错误信息:")
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    print("开始STAB-01修复验证测试...")
    
    success = await test_orchestrated_itinerary_fix()
    
    if success:
        print("\n✅ 所有测试通过！STAB-01修复成功。")
        return 0
    else:
        print("\n❌ 测试失败！需要进一步检查修复。")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code) 