"""
AutoPilot AI - Redis 缓存管理器

基于《数据流转与记忆体系.md》附录B的缓存键设计规范，
提供统一的工具调用结果缓存机制。

功能特性：
- 标准化的缓存键生成
- 差异化的TTL策略
- 异步Redis操作
- 缓存命中率统计
- 自动序列化/反序列化
"""

import json
import hashlib
from typing import Any, Dict, Optional, Union, Callable, TypeVar, Awaitable
from datetime import timedelta
from functools import wraps
import asyncio
import logging

import redis.asyncio as aioredis
from pydantic import BaseModel

from src.core.config import get_settings
from src.core.logger import get_logger

logger = get_logger(__name__)

T = TypeVar('T')


class CacheStats(BaseModel):
    """缓存统计信息"""
    
    total_requests: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    
    @property
    def hit_rate(self) -> float:
        """缓存命中率"""
        if self.total_requests == 0:
            return 0.0
        return self.cache_hits / self.total_requests


class CacheManager:
    """Redis 缓存管理器"""
    
    # 工具调用TTL配置（按照数据流转与记忆体系.md的建议）
    TTL_CONFIG = {
        # 地理编码 - 地理位置信息非常稳定
        'amap_maps_geo': timedelta(days=10).total_seconds(),
        'amap_geo': timedelta(days=10).total_seconds(),
        
        # 天气查询 - 天气预报在短期内有效
        'amap_maps_weather': timedelta(hours=4).total_seconds(), 
        'amap_weather': timedelta(hours=4).total_seconds(),
        
        # POI搜索 - POI信息可能会变化
        'amap_maps_search': timedelta(hours=24).total_seconds(),
        'amap_maps_text_search': timedelta(hours=24).total_seconds(),
        'amap_poi_search': timedelta(hours=24).total_seconds(),
        
        # 路线规划 - 交通路况具有实时性
        'amap_maps_direction_driving': timedelta(hours=2).total_seconds(),
        'amap_maps_direction_walking': timedelta(hours=2).total_seconds(),
        'amap_direction': timedelta(hours=2).total_seconds(),
        
        # 实时交通 - 实时性要求最高
        'amap_traffic': timedelta(minutes=5).total_seconds(),
        
        # 默认TTL
        'default': timedelta(hours=1).total_seconds()
    }
    
    def __init__(self):
        """初始化缓存管理器"""
        self.settings = get_settings()
        self._redis_client: Optional[aioredis.Redis] = None
        self._stats = CacheStats()
        self.logger = logger
    
    async def _get_redis_client(self) -> aioredis.Redis:
        """获取Redis客户端实例（延迟初始化）"""
        if self._redis_client is None:
            self._redis_client = aioredis.from_url(
                self.settings.redis_url,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5
            )
            
            # 测试连接
            try:
                await self._redis_client.ping()
                self.logger.info("Redis 连接建立成功")
            except Exception as e:
                self.logger.error(f"Redis 连接失败: {str(e)}")
                raise
        
        return self._redis_client
    
    def _generate_cache_key(self, tool_name: str, params: Dict[str, Any]) -> str:
        """
        生成标准化的缓存键
        
        按照数据流转与记忆体系.md附录B的规范：
        cache:tool:{tool_name}:{params_hash}
        
        Args:
            tool_name: 工具名称，如 'amap_maps_geo'
            params: 工具调用参数
            
        Returns:
            标准化的缓存键
        """
        # 规范化参数：按key排序，转为JSON字符串
        normalized_params = json.dumps(params, sort_keys=True, ensure_ascii=False)
        
        # 计算MD5哈希值
        params_hash = hashlib.md5(normalized_params.encode('utf-8')).hexdigest()
        
        # 生成标准缓存键
        cache_key = f"cache:tool:{tool_name}:{params_hash}"
        
        self.logger.debug(f"生成缓存键: {cache_key}")
        return cache_key
    
    def _get_ttl(self, tool_name: str) -> int:
        """
        获取工具的TTL配置
        
        Args:
            tool_name: 工具名称
            
        Returns:
            TTL秒数
        """
        return int(self.TTL_CONFIG.get(tool_name, self.TTL_CONFIG['default']))
    
    async def get(self, tool_name: str, params: Dict[str, Any]) -> Optional[Any]:
        """
        从缓存获取工具调用结果
        
        Args:
            tool_name: 工具名称
            params: 工具调用参数
            
        Returns:
            缓存的结果，如果未命中则返回None
        """
        try:
            redis_client = await self._get_redis_client()
            cache_key = self._generate_cache_key(tool_name, params)
            
            # 记录请求统计
            self._stats.total_requests += 1
            
            # 从Redis获取缓存
            cached_value = await redis_client.get(cache_key)
            
            if cached_value is not None:
                # 缓存命中
                self._stats.cache_hits += 1
                
                try:
                    result = json.loads(cached_value)
                    self.logger.info(
                        f"缓存命中: {tool_name}, 命中率: {self._stats.hit_rate:.2%}"
                    )
                    return result
                except json.JSONDecodeError as e:
                    self.logger.error(f"缓存数据反序列化失败: {str(e)}")
                    # 删除损坏的缓存
                    await redis_client.delete(cache_key)
            
            # 缓存未命中
            self._stats.cache_misses += 1
            self.logger.debug(
                f"缓存未命中: {tool_name}, 命中率: {self._stats.hit_rate:.2%}"
            )
            return None
            
        except Exception as e:
            self.logger.error(f"缓存读取失败: {str(e)}")
            self._stats.cache_misses += 1
            return None
    
    async def set(
        self, 
        tool_name: str, 
        params: Dict[str, Any], 
        result: Any,
        ttl_override: Optional[int] = None
    ) -> bool:
        """
        将工具调用结果写入缓存
        
        Args:
            tool_name: 工具名称
            params: 工具调用参数
            result: 工具调用结果
            ttl_override: 可选的TTL覆盖值（秒）
            
        Returns:
            写入是否成功
        """
        try:
            redis_client = await self._get_redis_client()
            cache_key = self._generate_cache_key(tool_name, params)
            
            # 序列化结果
            serialized_result = json.dumps(result, ensure_ascii=False)
            
            # 确定TTL
            ttl = ttl_override if ttl_override is not None else self._get_ttl(tool_name)
            
            # 写入Redis
            success = await redis_client.setex(cache_key, ttl, serialized_result)
            
            if success:
                self.logger.info(f"缓存写入成功: {tool_name}, TTL: {ttl}s")
            else:
                self.logger.warning(f"缓存写入失败: {tool_name}")
            
            return bool(success)
            
        except Exception as e:
            self.logger.error(f"缓存写入失败: {str(e)}")
            return False
    
    async def delete(self, tool_name: str, params: Dict[str, Any]) -> bool:
        """
        删除特定的缓存项
        
        Args:
            tool_name: 工具名称  
            params: 工具调用参数
            
        Returns:
            删除是否成功
        """
        try:
            redis_client = await self._get_redis_client()
            cache_key = self._generate_cache_key(tool_name, params)
            
            deleted_count = await redis_client.delete(cache_key)
            success = deleted_count > 0
            
            if success:
                self.logger.info(f"缓存删除成功: {tool_name}")
            else:
                self.logger.debug(f"缓存项不存在: {tool_name}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"缓存删除失败: {str(e)}")
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """
        批量删除匹配模式的缓存项
        
        Args:
            pattern: Redis SCAN模式，如 'cache:tool:amap_*'
            
        Returns:
            删除的缓存项数量
        """
        try:
            redis_client = await self._get_redis_client()
            
            deleted_count = 0
            async for key in redis_client.scan_iter(match=pattern):
                await redis_client.delete(key)
                deleted_count += 1
            
            self.logger.info(f"批量删除缓存: {pattern}, 删除数量: {deleted_count}")
            return deleted_count
            
        except Exception as e:
            self.logger.error(f"批量删除缓存失败: {str(e)}")
            return 0
    
    def get_stats(self) -> CacheStats:
        """获取缓存统计信息"""
        return self._stats.model_copy()
    
    def reset_stats(self) -> None:
        """重置缓存统计信息"""
        self._stats = CacheStats()
        self.logger.info("缓存统计信息已重置")
    
    async def close(self) -> None:
        """关闭Redis连接"""
        if self._redis_client is not None:
            await self._redis_client.close()
            self._redis_client = None
            self.logger.info("Redis 连接已关闭")


def cached_tool_call(tool_name: str, ttl_override: Optional[int] = None):
    """
    工具调用缓存装饰器
    
    自动为异步工具函数添加缓存机制。
    
    Args:
        tool_name: 工具名称，用于缓存键生成
        ttl_override: 可选的TTL覆盖值
        
    Usage:
        @cached_tool_call('amap_maps_geo')
        async def search_location(address: str) -> dict:
            # 实际的API调用逻辑
            pass
    """
    def decorator(func: Callable[..., Awaitable[T]]) -> Callable[..., Awaitable[T]]:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> T:
            # 创建缓存管理器实例
            cache_manager = CacheManager()
            
            try:
                # 构建参数字典用于缓存键生成
                params = {}
                
                # 处理位置参数
                if args:
                    import inspect
                    sig = inspect.signature(func)
                    param_names = list(sig.parameters.keys())
                    for i, arg in enumerate(args):
                        if i < len(param_names):
                            params[param_names[i]] = arg
                
                # 处理关键字参数
                params.update(kwargs)
                
                # 先尝试从缓存获取
                cached_result = await cache_manager.get(tool_name, params)
                if cached_result is not None:
                    return cached_result
                
                # 缓存未命中，执行实际函数
                result = await func(*args, **kwargs)
                
                # 将结果写入缓存
                await cache_manager.set(tool_name, params, result, ttl_override)
                
                return result
                
            except Exception as e:
                logger.error(f"缓存装饰器执行失败: {str(e)}")
                # 发生错误时直接执行原函数
                return await func(*args, **kwargs)
                
            finally:
                # 清理连接
                await cache_manager.close()
        
        return wrapper
    return decorator


# 全局缓存管理器实例
_global_cache_manager: Optional[CacheManager] = None


async def get_cache_manager() -> CacheManager:
    """获取全局缓存管理器实例"""
    global _global_cache_manager
    if _global_cache_manager is None:
        _global_cache_manager = CacheManager()
    return _global_cache_manager 