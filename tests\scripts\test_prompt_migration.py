#!/usr/bin/env python3
"""
AutoPilot AI - Prompt 迁移测试脚本

测试新的 Jinja2 Prompt 管理系统，验证：
1. 模板渲染功能
2. JSON Schema 集成
3. 与原有系统的兼容性
4. 性能和可用性
"""

import asyncio
import json
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.prompts import get_prompt_manager, get_travel_planner_prompts, PromptContext
from src.models.travel_planner import UserProfile
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_prompt_manager_basic():
    """测试 Prompt 管理器基础功能"""
    print("🔍 测试 1: Prompt 管理器基础功能")
    
    manager = get_prompt_manager()
    
    # 测试模板列表
    templates = manager.list_templates()
    print(f"   找到模板文件: {len(templates)} 个")
    for template in templates:
        print(f"     - {template}")
    
    # 测试 Schema 加载
    schema = manager.load_json_schema("extract_travel_intent")
    print(f"   Schema 加载: {'✅ 成功' if schema else '❌ 失败'}")
    
    print("   ✅ 基础功能测试通过\n")


def test_intent_extraction_prompt():
    """测试意图提取 Prompt 渲染"""
    print("🔍 测试 2: 意图提取 Prompt 渲染")
    
    prompts = get_travel_planner_prompts()
    
    # 测试场景
    test_cases = [
        "我想去杭州玩三天，有什么好的推荐吗？",
        "周末带孩子去北京，预算不高",
        "明天开车去天津，一日游"
    ]
    
    for i, query in enumerate(test_cases, 1):
        try:
            prompt = prompts.render_intent_extraction_prompt(
                user_query=query,
                current_datetime="2024年12月20日 周五"
            )
            
            print(f"   测试用例 {i}: {query}")
            print(f"     生成 Prompt 长度: {len(prompt)} 字符")
            print(f"     包含 Schema: {'✅ 是' if 'JSON' in prompt else '❌ 否'}")
            print(f"     包含用户查询: {'✅ 是' if query in prompt else '❌ 否'}")
            
            # 显示 Prompt 片段
            preview = prompt[:200] + "..." if len(prompt) > 200 else prompt
            print(f"     Prompt 预览: {preview}\n")
            
        except Exception as e:
            print(f"   ❌ 测试用例 {i} 失败: {str(e)}\n")
    
    print("   ✅ 意图提取 Prompt 测试完成\n")


def test_poi_scoring_prompt():
    """测试 POI 评分 Prompt 渲染"""
    print("🔍 测试 3: POI 评分 Prompt 渲染")
    
    prompts = get_travel_planner_prompts()
    
    # 构建测试上下文
    context = PromptContext(
        user_id="test_user_001",
        trace_id="test_trace_001",
        extracted_entities={
            "destination": "杭州",
            "days": 3,
            "budget": "中等",
            "preferences": ["文化历史", "美食体验"]
        },
        user_profile=UserProfile(
            user_id="test_user_001",
            travel_style="休闲",
            budget_preference="中等",
            tags=["文化爱好者", "美食达人"],
            favorite_activities=["博物馆", "特色餐厅"]
        ),
        tool_results={
            "poi_景点": {"pois": [{"name": "西湖", "rating": 4.8}]},
            "poi_美食": {"pois": [{"name": "知味观", "rating": 4.5}]}
        }
    )
    
    # 模拟 POI 候选
    poi_candidates = [
        {"id": "poi_001", "name": "西湖", "category": "景点", "rating": 4.8},
        {"id": "poi_002", "name": "知味观", "category": "美食", "rating": 4.5},
        {"id": "poi_003", "name": "灵隐寺", "category": "景点", "rating": 4.7}
    ]
    
    try:
        prompt = prompts.render_poi_scoring_prompt(
            context=context,
            poi_candidates=poi_candidates
        )
        
        print(f"   生成 POI 评分 Prompt 长度: {len(prompt)} 字符")
        print(f"   包含用户画像: {'✅ 是' if 'travel_style' in prompt else '❌ 否'}")
        print(f"   包含 POI 候选: {'✅ 是' if '西湖' in prompt else '❌ 否'}")
        print(f"   包含评分标准: {'✅ 是' if '评分标准' in prompt else '❌ 否'}")
        
        # 显示 Prompt 关键片段
        lines = prompt.split('\n')
        for i, line in enumerate(lines[:15]):
            print(f"     {i+1:2d}: {line}")
        
        print("   ✅ POI 评分 Prompt 测试通过\n")
        
    except Exception as e:
        print(f"   ❌ POI 评分 Prompt 测试失败: {str(e)}\n")


def test_accommodation_decision_prompt():
    """测试住宿决策 Prompt 渲染"""
    print("🔍 测试 4: 住宿决策 Prompt 渲染")
    
    prompts = get_travel_planner_prompts()
    
    # 构建测试上下文
    context = PromptContext(
        extracted_entities={
            "destination": "苏州",
            "origin": "上海",
            "days": 2,
            "travelers": "情侣出行",
            "budget": "中等"
        },
        user_profile=UserProfile(
            user_id="test_user_002",
            travel_style="浪漫",
            budget_preference="中等"
        )
    )
    
    try:
        prompt = prompts.render_accommodation_decision_prompt(context=context)
        
        print(f"   生成住宿决策 Prompt 长度: {len(prompt)} 字符")
        print(f"   包含距离分析: {'✅ 是' if '距离因素' in prompt else '❌ 否'}")
        print(f"   包含策略建议: {'✅ 是' if '住宿策略' in prompt else '❌ 否'}")
        print(f"   包含人员考虑: {'✅ 是' if '情侣出行' in prompt else '❌ 否'}")
        
        print("   ✅ 住宿决策 Prompt 测试通过\n")
        
    except Exception as e:
        print(f"   ❌ 住宿决策 Prompt 测试失败: {str(e)}\n")


def test_fallback_mechanism():
    """测试后备机制"""
    print("🔍 测试 5: 后备机制测试")
    
    prompts = get_travel_planner_prompts()
    
    # 测试不存在的模板
    try:
        # 直接访问模板管理器，模拟模板文件缺失
        manager = prompts.template_manager
        
        # 尝试加载不存在的模板
        try:
            manager.load_template("non_existent_template.md")
            print("   ❌ 应该抛出异常但没有")
        except Exception:
            print("   ✅ 正确处理了模板缺失")
        
        # 测试后备 Prompt
        fallback_prompt = prompts._get_fallback_intent_prompt(
            "测试查询", "2024年12月20日 周五"
        )
        
        print(f"   后备 Prompt 长度: {len(fallback_prompt)} 字符")
        print(f"   包含基本结构: {'✅ 是' if '请分析用户' in fallback_prompt else '❌ 否'}")
        
        print("   ✅ 后备机制测试通过\n")
        
    except Exception as e:
        print(f"   ❌ 后备机制测试失败: {str(e)}\n")


def test_template_syntax_validation():
    """测试模板语法验证"""
    print("🔍 测试 6: 模板语法验证")
    
    manager = get_prompt_manager()
    templates = manager.list_templates()
    
    valid_count = 0
    total_count = len(templates)
    
    for template in templates:
        is_valid = manager.validate_template_syntax(template)
        status = "✅ 有效" if is_valid else "❌ 无效"
        print(f"   {template}: {status}")
        
        if is_valid:
            valid_count += 1
    
    print(f"\n   语法验证结果: {valid_count}/{total_count} 个模板有效")
    
    if valid_count == total_count:
        print("   ✅ 所有模板语法验证通过\n")
    else:
        print("   ⚠️  部分模板存在语法问题\n")


def test_performance_comparison():
    """测试性能对比"""
    print("🔍 测试 7: 性能对比测试")
    
    import time
    
    prompts = get_travel_planner_prompts()
    
    # 测试新 Prompt 系统性能
    start_time = time.time()
    
    for i in range(10):
        prompt = prompts.render_intent_extraction_prompt(
            user_query=f"测试查询 {i}",
            current_datetime="2024年12月20日 周五"
        )
    
    new_system_time = time.time() - start_time
    
    # 测试后备系统性能
    start_time = time.time()
    
    for i in range(10):
        prompt = prompts._get_fallback_intent_prompt(
            f"测试查询 {i}", "2024年12月20日 周五"
        )
    
    fallback_time = time.time() - start_time
    
    print(f"   新 Prompt 系统 (10次): {new_system_time:.4f} 秒")
    print(f"   后备系统 (10次): {fallback_time:.4f} 秒")
    print(f"   性能比率: {new_system_time/fallback_time:.2f}x")
    
    if new_system_time < fallback_time * 2:
        print("   ✅ 性能表现良好\n")
    else:
        print("   ⚠️  性能可能需要优化\n")


def main():
    """运行所有测试"""
    print("🚀 开始 AutoPilot AI Prompt 迁移测试\n")
    print("=" * 60)
    
    tests = [
        test_prompt_manager_basic,
        test_intent_extraction_prompt,
        test_poi_scoring_prompt,
        test_accommodation_decision_prompt,
        test_fallback_mechanism,
        test_template_syntax_validation,
        test_performance_comparison
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_func in tests:
        try:
            test_func()
            passed_tests += 1
        except Exception as e:
            print(f"   ❌ 测试失败: {str(e)}\n")
    
    print("=" * 60)
    print(f"🎯 测试完成: {passed_tests}/{total_tests} 个测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！Prompt 迁移成功！")
        return 0
    else:
        print("⚠️  部分测试失败，需要检查问题")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 