"""
AutoPilot AI - Prompt 模板管理器

基于 Jinja2 的现代化 Prompt 模板管理系统，提供：
- 动态模板渲染
- JSON Schema 集成  
- 模板缓存机制
- 多语言支持
- 类型安全的上下文管理
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional, Union, List
from dataclasses import dataclass, field
from datetime import datetime
import logging

from jinja2 import Environment, FileSystemLoader, Template, select_autoescape
from jinja2.exceptions import TemplateNotFound, TemplateSyntaxError

logger = logging.getLogger(__name__)


@dataclass
class PromptContext:
    """Prompt 渲染上下文数据类"""
    
    # 基础信息
    user_id: Optional[str] = None
    trace_id: Optional[str] = None
    timestamp: Optional[datetime] = field(default_factory=datetime.now)
    
    # 用户相关
    user_query: Optional[str] = None
    user_profile: Optional[Dict[str, Any]] = None
    user_memories: Optional[List[Dict[str, Any]]] = None
    
    # 任务相关
    extracted_entities: Optional[Dict[str, Any]] = None
    tool_results: Optional[Dict[str, Any]] = None
    current_step: Optional[str] = None
    
    # 系统信息
    current_time: Optional[str] = None
    system_config: Optional[Dict[str, Any]] = None
    
    # 自定义数据
    custom_data: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，用于 Jinja2 渲染"""
        result = {}
        for key, value in self.__dict__.items():
            if value is not None:
                # 处理 datetime 对象
                if isinstance(value, datetime):
                    result[key] = value.strftime("%Y年%m月%d日 %H:%M:%S")
                else:
                    result[key] = value
        return result
    
    def merge(self, other_context: 'PromptContext') -> 'PromptContext':
        """合并两个上下文对象"""
        merged = PromptContext()
        
        # 复制当前对象的所有非空字段
        for key, value in self.__dict__.items():
            if value is not None:
                setattr(merged, key, value)
        
        # 用另一个对象的非空字段覆盖
        for key, value in other_context.__dict__.items():
            if value is not None:
                setattr(merged, key, value)
                
        return merged


class PromptTemplateManager:
    """基于 Jinja2 的 Prompt 模板管理器"""
    
    def __init__(self, templates_dir: Optional[Union[str, Path]] = None):
        """
        初始化模板管理器
        
        Args:
            templates_dir: 模板文件目录，默认为 src/prompts
        """
        if templates_dir is None:
            # 获取当前文件的目录，默认为 src/prompts
            current_dir = Path(__file__).parent
            templates_dir = current_dir
        
        self.templates_dir = Path(templates_dir)
        
        # 创建 Jinja2 环境
        self.env = Environment(
            loader=FileSystemLoader(str(self.templates_dir)),
            autoescape=select_autoescape(['html', 'xml']),
            trim_blocks=True,
            lstrip_blocks=True,
            keep_trailing_newline=False
        )
        
        # 注册自定义过滤器和函数
        self._register_custom_functions()
        
        # 模板缓存
        self._template_cache: Dict[str, Template] = {}
        
        logger.info(f"PromptTemplateManager 初始化完成，模板目录: {self.templates_dir}")
    
    def _register_custom_functions(self):
        """注册自定义的 Jinja2 函数和过滤器"""

        # --- 自定义过滤器 ---
        def json_pretty_filter(value: Any) -> str:
            """格式化 JSON 输出"""
            if isinstance(value, str):
                try:
                    value = json.loads(value)
                except json.JSONDecodeError:
                    return value
            return json.dumps(value, ensure_ascii=False, indent=2)

        def truncate_smart_filter(value: str, length: int = 100) -> str:
            """智能截断文本，保持完整的句子"""
            if len(value) <= length:
                return value
            
            # 在指定长度附近找最近的句号或逗号
            truncated = value[:length]
            last_punct = max(truncated.rfind('。'), truncated.rfind('，'), truncated.rfind('；'))
            
            if last_punct > length * 0.7:  # 如果标点符号在合理位置
                return truncated[:last_punct + 1]
            else:
                return truncated + "..."
        
        self.env.filters['json_pretty'] = json_pretty_filter
        self.env.filters['truncate_smart'] = truncate_smart_filter

        # --- 自定义全局函数 ---
        def current_time_cn():
            """获取中文格式的当前时间"""
            now = datetime.now()
            weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
            weekday = weekdays[now.weekday()]
            return f"{now.strftime('%Y年%m月%d日')} {weekday}"
        
        def format_preferences(preferences: List[str], max_items: int = 5) -> str:
            """格式化用户偏好列表"""
            if not preferences:
                return "无特殊偏好"
            
            limited_prefs = preferences[:max_items]
            result = "、".join(limited_prefs)
            
            if len(preferences) > max_items:
                result += f"等{len(preferences)}项偏好"
                
            return result
        
        def extract_poi_summary(tool_results: Dict[str, Any], category: str) -> str:
            """从工具结果中提取 POI 摘要"""
            key = f"poi_{category}"
            if key not in tool_results:
                return f"暂未获取到{category}信息"
            
            pois = tool_results[key].get('pois', [])
            if not pois:
                return f"未找到相关{category}"
            
            return f"找到{len(pois)}个{category}选项"
        
        self.env.globals['current_time_cn'] = current_time_cn
        self.env.globals['format_preferences'] = format_preferences
        self.env.globals['extract_poi_summary'] = extract_poi_summary
    
    def load_template(self, template_name: str, use_cache: bool = True) -> Template:
        """
        加载模板文件
        
        Args:
            template_name: 模板文件名（相对于 templates_dir）
            use_cache: 是否使用缓存
            
        Returns:
            Jinja2 模板对象
            
        Raises:
            TemplateNotFound: 模板文件不存在
            TemplateSyntaxError: 模板语法错误
        """
        if use_cache and template_name in self._template_cache:
            return self._template_cache[template_name]
        
        try:
            template = self.env.get_template(template_name)
            
            if use_cache:
                self._template_cache[template_name] = template
                
            logger.debug(f"成功加载模板: {template_name}")
            return template
            
        except TemplateNotFound:
            logger.error(f"模板文件不存在: {template_name}")
            raise
        except TemplateSyntaxError as e:
            logger.error(f"模板语法错误 {template_name}: {str(e)}")
            raise
    
    def render_template(
        self, 
        template_name: str, 
        context: Union[PromptContext, Dict[str, Any]], 
        **kwargs
    ) -> str:
        """
        渲染模板
        
        Args:
            template_name: 模板文件名
            context: 渲染上下文（PromptContext 对象或字典）
            **kwargs: 额外的上下文变量
            
        Returns:
            渲染后的文本
        """
        template = self.load_template(template_name)
        
        # 准备渲染上下文
        if isinstance(context, PromptContext):
            render_context = context.to_dict()
        else:
            render_context = dict(context)
        
        # 合并额外的上下文变量
        render_context.update(kwargs)
        
        try:
            rendered = template.render(**render_context)
            logger.debug(f"成功渲染模板: {template_name}")
            return rendered.strip()
            
        except Exception as e:
            logger.error(f"模板渲染失败 {template_name}: {str(e)}")
            logger.error(f"渲染上下文: {list(render_context.keys())}")
            raise
    
    def render_template_with_schema(
        self,
        template_name: str,
        context: Union[PromptContext, Dict[str, Any]],
        schema_name: str,
        schema_content: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> str:
        """
        渲染包含 JSON Schema 的模板
        
        Args:
            template_name: 主模板文件名
            context: 渲染上下文
            schema_name: Schema 名称
            schema_content: Schema 内容（如果为 None，会从文件加载）
            **kwargs: 额外的上下文变量
            
        Returns:
            包含 Schema 的渲染后文本
        """
        # 加载 Schema
        if schema_content is None:
            schema_content = self.load_json_schema(schema_name)
        
        # 准备上下文
        if isinstance(context, PromptContext):
            render_context = context.to_dict()
        else:
            render_context = dict(context)
        
        # 添加 Schema 到上下文
        render_context.update({
            'schema_name': schema_name,
            'schema_content': schema_content,
            'formatted_schema': json.dumps(schema_content, ensure_ascii=False, indent=2),
            **kwargs
        })
        
        return self.render_template(template_name, render_context)
    
    def load_json_schema(self, schema_name: str) -> Dict[str, Any]:
        """
        加载 JSON Schema 文件
        
        Args:
            schema_name: Schema 文件名（不含扩展名）
            
        Returns:
            Schema 字典
        """
        schema_path = self.templates_dir / "schemas" / f"{schema_name}.json"
        
        if not schema_path.exists():
            logger.warning(f"Schema 文件不存在: {schema_path}")
            return {}
        
        try:
            with open(schema_path, 'r', encoding='utf-8') as f:
                schema = json.load(f)
            logger.debug(f"成功加载 Schema: {schema_name}")
            return schema
            
        except json.JSONDecodeError as e:
            logger.error(f"Schema 格式错误 {schema_name}: {str(e)}")
            return {}
        except Exception as e:
            logger.error(f"加载 Schema 失败 {schema_name}: {str(e)}")
            return {}
    
    def list_templates(self, pattern: str = "*.md") -> List[str]:
        """
        列出所有模板文件
        
        Args:
            pattern: 文件匹配模式
            
        Returns:
            模板文件名列表
        """
        try:
            templates = []
            for path in self.templates_dir.rglob(pattern):
                if path.is_file():
                    # 获取相对于 templates_dir 的路径
                    relative_path = path.relative_to(self.templates_dir)
                    templates.append(str(relative_path))
            
            logger.debug(f"找到 {len(templates)} 个模板文件")
            return sorted(templates)
            
        except Exception as e:
            logger.error(f"列出模板文件失败: {str(e)}")
            return []
    
    def validate_template_syntax(self, template_name: str) -> bool:
        """
        验证模板语法
        
        Args:
            template_name: 模板文件名
            
        Returns:
            语法是否正确
        """
        try:
            self.load_template(template_name, use_cache=False)
            return True
        except (TemplateNotFound, TemplateSyntaxError):
            return False
    
    def clear_cache(self):
        """清空模板缓存"""
        self._template_cache.clear()
        logger.info("模板缓存已清空")
    
    def get_template_info(self, template_name: str) -> Dict[str, Any]:
        """
        获取模板信息
        
        Args:
            template_name: 模板文件名
            
        Returns:
            模板信息字典
        """
        template_path = self.templates_dir / template_name
        
        if not template_path.exists():
            return {"exists": False}
        
        stat = template_path.stat()
        
        return {
            "exists": True,
            "path": str(template_path),
            "size": stat.st_size,
            "modified": datetime.fromtimestamp(stat.st_mtime),
            "syntax_valid": self.validate_template_syntax(template_name)
        } 